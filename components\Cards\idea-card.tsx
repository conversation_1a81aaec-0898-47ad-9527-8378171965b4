"use client";

import { PlusSquare } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface IdeaCardProps {
    image: string;
    title: string;
    avgPrice: string;
    onUpload?: () => void;
    className?: string;
    avgPriceUnit?: string;
}

export function IdeaCard({
    image,
    title,
    avgPrice,
    onUpload,
    className,
    avgPriceUnit = "day",
}: IdeaCardProps) {
    const unitLabel = avgPriceUnit.toLowerCase() === "hour" ? "an hour" : `a ${avgPriceUnit}`;
    return (
        <div
            className={
                cn(
                    "rounded-lg shadow-lg",
                    className
                )
            }
        >
            {/* Image Section */}
            <div className="w-full aspect-[1.4/1] rounded-t-lg bg-gray-100" >
                <Image
                    src={image}
                    alt={title}
                    className="w-full rounded-t-lg h-full object-cover"
                    width={320}
                    height={230}
                />
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col justify-between p-5 pb-4" >
                <div>
                    <h3 className="text-brand-600 text-xl font-normal mb-1" > {title} </h3>
                    < p className="text-brand-300 text-sm " > Avg ${avgPrice} {unitLabel} </p>
                </div>
            </div>

            {/* Upload Button */}
            <div className="px-5 pb-5" >
                <button
                    type="button"
                    onClick={onUpload}
                    className="cursor-pointer w-full flex items-center justify-center gap-2 bg-brand-1100 text-brand-100 font-medium rounded-lg py-2.5 text-sm hover:bg-brand-1000 hover:text-white hover:shadow-customHover"
                >
                    Upload < PlusSquare size={18} />
                </button>
            </div>
        </div>
    );
} 