import { Ban, Check, ShieldCheck } from "lucide-react";

export default function PricingModal() {
    return (
        <div className="bg-[url('/images/bg-pricing.png')] bg-cover bg-center bg-no-repeat mt-40">
            <div className="container mx-auto ">
                <div className="text-center mb-20">
                    <p className="text-brand-100 text-3xl font-light">Does YouHook have fees?</p>
                    <h2 className="text-brand-600 font-medium text-7xl">Our pricing model</h2>
                </div>
                <div className="grid grid-cols-2 gap-x-10">
                    <div className="border-[0.5px] border-brand-300/50 rounded-2xl px-28 pt-16 pb-16 flex flex-col">
                        <div className="border-b border-brand-gray-100/50 text-center">
                            <h3 className="font-light text-brand-600 text-5xl mb-4">
                                Sign up later
                            </h3>
                            <p className="font-normal text-brand-200 text-xl mb-10">
                                Get account <span className="font-medium">with fees</span>
                            </p>
                        </div>
                        <h4 className="font-medium text-center text-brand-gray-200 text-6xl mt-8 mb-10">
                            34% <span className="font-normal text-2xl">Fee</span>
                        </h4>
                        <ul className="space-y-7">
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    15% Renters fee
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    18% Owners fee
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Produce more waste
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    No extra income
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Buy extra items
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-gray-150">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Miss your opportunity
                                </p>
                            </li>
                        </ul>
                        <button className="bg-brand-gray-250 font-medium text-2xl  rounded-2xl cursor-pointer text-white text-center w-full h-[75px] flex items-center justify-center mt-auto">
                            I’ll wait
                        </button>
                    </div>
                    <div className="border-[0.5px] border-brand-300/50 rounded-2xl px-28 bg-white pt-16 pb-16">
                        <div className="border-b border-brand-gray-100/50 text-center">
                            <h3 className="font-medium text-brand-600 text-5xl mb-4">
                                Sign up today
                            </h3>
                            <p className="font-normal text-brand-200 text-xl mb-10">
                                Get a <span className="font-medium text-brand-blue-50">free </span>account
                            </p>
                        </div>
                        <h4 className="text-center mt-8 mb-10">
                            <span className="font-regular text-brand-gray-200 text-4xl">33%</span>
                            <span className="text-brand-blue-50 font-medium text-6xl"> 0% </span>
                            <span className="text-brand-gray-300 text-4xl">Fee</span>
                        </h4>
                        <ul className="space-y-7">
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    15% Renters fee
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    18% Owners fee
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Produce more waste
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    No extra income
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Buy extra items
                                </p>
                            </li>
                            <li className="flex gap-4 text-brand-600">
                                <span className="mt-1"><Check className="w-6 h-6" /></span>
                                <p className="font-normal text-2xl">
                                    Miss your opportunity
                                </p>
                            </li>
                        </ul>
                        <div className="border-t border-brand-gray-100 px-8 space-y-3 pt-10 mt-12">
                            <p className="font-medium text-brand-600 text-2xl flex items-center gap-3">
                                <span><ShieldCheck className="w-6 h-6" /></span>
                                <span>Guaranteed coverage</span>
                            </p>
                            <p className="font-medium text-brand-blue-50 text-2xl flex items-center gap-3">
                                <span><Ban className="w-6 h-6" /></span>
                                <span>No hidden fees</span>
                            </p>
                            <p className="font-normal text-xl text-brand-black-100">
                                Sign up before offer ends
                            </p>
                        </div>
                        <div className="mt-24">
                            <button className="text-2xl font-medium secondary-button text-white py-4 rounded-xl w-full h-[75px] flex items-center justify-center cursor-pointer">
                                Join YouHook
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}