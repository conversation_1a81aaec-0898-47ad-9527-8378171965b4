'use client'
import React, { useEffect, useRef, useState } from "react";
import { MapPin, Search as SearchIcon, Send, Clock } from "lucide-react";
import { Loader } from "@googlemaps/js-api-loader";
import { ideaCardData } from "@/data";
import { useRouter } from "next/navigation";

const SearchBar = () => {
    const router = useRouter();

    // -----------------------
    //  "What are you looking for?" state
    // -----------------------
    const [query, setQuery] = useState<string>("");
    const [isQueryFocused, setIsQueryFocused] = useState<boolean>(false);
    const [filteredQuerySuggestions, setFilteredQuerySuggestions] = useState<string[]>([]);

    // Search history management
    const [searchHistory, setSearchHistory] = useState<{ queries: string[], locations: string[] }>({
        queries: [],
        locations: []
    });

    // Load search history from localStorage on mount
    useEffect(() => {
        const savedHistory = localStorage.getItem('youhook-search-history');
        if (savedHistory) {
            setSearchHistory(JSON.parse(savedHistory));
        }
    }, []);

    // Save to search history
    const saveToHistory = (query: string, location: string) => {
        const newHistory = {
            queries: [query, ...searchHistory.queries.filter(q => q !== query)].slice(0, 10),
            locations: [location, ...searchHistory.locations.filter(l => l !== location)].slice(0, 10)
        };
        setSearchHistory(newHistory);
        localStorage.setItem('youhook-search-history', JSON.stringify(newHistory));
    };

    // -----------------------
    //  Location state (Google Places Autocomplete)
    // -----------------------
    const [location, setLocation] = useState<string>("");
    const [isLocationFocused, setIsLocationFocused] = useState<boolean>(false);
    const [locationPredictions, setLocationPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
    const autocompleteServiceRef = useRef<google.maps.places.AutocompleteService | null>(null);

    // -----------------------
    //  Refs to inputs (for focusing on validation)
    // -----------------------
    const queryInputRef = useRef<HTMLInputElement | null>(null);
    const locationInputRef = useRef<HTMLInputElement | null>(null);

    // -----------------------
    //  Load Google Maps JS API once on mount
    // -----------------------
    useEffect(() => {
        const loader = new Loader({
            apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ?? "", // must be provided in env
            libraries: ["places"],
        });
        loader.load().then(() => {
            if (typeof window !== "undefined") {
                const win = window as typeof window & { google?: typeof google };
                if (win.google) {
                    autocompleteServiceRef.current = new google.maps.places.AutocompleteService();
                }
            }
        });
    }, []);

    // -----------------------
    //  Filter query suggestions whenever the query changes
    // -----------------------
    useEffect(() => {
        if (!query.trim()) {
            setFilteredQuerySuggestions([]);
            return;
        }
        const lower = query.toLowerCase();
        const allSuggestions = staticSuggestions;
        const filtered = allSuggestions.filter((title) => title.toLowerCase().includes(lower));
        setFilteredQuerySuggestions(filtered.slice(0, 6)); // show at most 6 suggestions
    }, [query]);

    // -----------------------
    //  Fetch Google Places predictions when location input changes
    // -----------------------
    useEffect(() => {
        if (!autocompleteServiceRef.current || !location.trim()) {
            setLocationPredictions([]);
            return;
        }
        const svc = autocompleteServiceRef.current;
        svc.getPlacePredictions({ input: location, types: ["(cities)"] }, (preds) => {
            setLocationPredictions(preds || []);
        });
    }, [location]);

    // -----------------------
    //  Click outside handler to close suggestion lists
    // -----------------------
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (!(e.target instanceof Node)) return;
            if (
                queryInputRef.current &&
                !queryInputRef.current.contains(e.target) &&
                locationInputRef.current &&
                !locationInputRef.current.contains(e.target)
            ) {
                setIsQueryFocused(false);
                setIsLocationFocused(false);
            }
        };
        document.addEventListener("click", handleClickOutside);
        return () => document.removeEventListener("click", handleClickOutside);
    }, []);

    // -----------------------
    //  Form submit handler
    // -----------------------
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!location.trim()) {
            locationInputRef.current?.focus();
            return;
        }
        if (!query.trim()) {
            queryInputRef.current?.focus();
            return;
        }

        // Save to history before navigation
        saveToHistory(query, location);
        router.push(`/explore?query=${encodeURIComponent(query)}&location=${encodeURIComponent(location)}`);
    };

    // Helper to render a generic suggestion list container
    const SuggestionsContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
        <div className="absolute left-0 right-0 top-full mt-10 py-10 bg-white rounded-2xl shadow-custom-4 z-50 h-[411px] overflow-auto no-scroll w-[120%]">
            {children}
        </div>
    );

    // Highlight typed portion within suggestion (typed part black, remainder gray)
    const renderHighlighted = (text: string, typed: string) => {
        const idx = text.toLowerCase().indexOf(typed.toLowerCase());
        if (idx === -1 || !typed) return <span className="text-brand-gray-1005">{text}</span>;
        const before = text.slice(0, idx);
        const match = text.slice(idx, idx + typed.length);
        const after = text.slice(idx + typed.length);
        return (
            <>
                {before && <span className="text-brand-gray-1005">{before}</span>}
                <span className="text-black">{match}</span>
                {after && <span className="text-brand-gray-1005">{after}</span>}
            </>
        );
    };

    // Static suggestions from data
    const staticSuggestions = ideaCardData.map((item) => item.title);

    return (
        <form
            onSubmit={handleSubmit}
            className="flex flex-col sm:flex-row items-center bg-white rounded-full shadow-lg px-4 py-3 gap-2 sm:gap-0 w-full relative"
        >
            {/* Location input */}
            <div className="relative flex items-center px-2 w-full sm:w-1/3">
                <MapPin className="text-gray-400 mr-2" size={20} />
                <input
                    ref={locationInputRef}
                    type="text"
                    placeholder="Enter a location"
                    value={location}
                    onFocus={() => {
                        setIsLocationFocused(true);
                        setIsQueryFocused(false);
                    }}
                    onChange={(e) => setLocation(e.target.value)}
                    className="outline-none bg-transparent w-full text-gray-700 placeholder-gray-400"
                />
                {/* Suggestions for location */}
                {isLocationFocused && (
                    <SuggestionsContainer>
                        {location.trim() === "" ? (
                            <>
                                {/* Current Location Option */}
                                <div
                                    className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                    onClick={async () => {
                                        if (!navigator.geolocation) return;
                                        navigator.geolocation.getCurrentPosition((pos) => {
                                            const { latitude, longitude } = pos.coords;
                                            if (typeof google !== "undefined") {
                                                const geocoder = new google.maps.Geocoder();
                                                geocoder.geocode({ location: { lat: latitude, lng: longitude } }, (results) => {
                                                    if (results && results[0]) {
                                                        setLocation(results[0].formatted_address);
                                                        setIsLocationFocused(false);
                                                    }
                                                });
                                            }
                                        });
                                    }}
                                >
                                    <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                        <Send className="text-brand-gray-1005" size={20} />
                                    </span>
                                    Use Current Location
                                </div>

                                {/* Location History */}
                                {searchHistory.locations.map((historyLocation, idx) => (
                                    <div
                                        key={idx}
                                        className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                        onClick={() => {
                                            setLocation(historyLocation);
                                            setIsLocationFocused(false);
                                        }}
                                    >
                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                            <Clock className="text-brand-gray-1005" size={20} />
                                        </span>
                                        <span className="text-black">{historyLocation}</span>
                                    </div>
                                ))}
                            </>
                        ) : (
                            <>
                                {locationPredictions.length === 0 && (
                                    <p className="px-4 py-3 text-gray-400 text-sm">Searching...</p>
                                )}
                                {locationPredictions.map((pred) => (
                                    <div
                                        key={pred.place_id}
                                        className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                        onClick={() => {
                                            setLocation(pred.description);
                                            setIsLocationFocused(false);
                                        }}
                                    >
                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                            <MapPin className="text-brand-gray-1005" size={20} />
                                        </span>
                                        <span className="text-black">{pred.description}</span>
                                    </div>
                                ))}
                            </>
                        )}
                    </SuggestionsContainer>
                )}
            </div>

            {/* Divider */}
            <div className="w-px h-6 bg-gray-200 mx-2 hidden sm:block" />

            {/* Query input */}
            <div className="relative flex items-center px-2 w-full sm:w-2/3">
                <SearchIcon className="text-gray-400 mr-2" size={20} />
                <input
                    ref={queryInputRef}
                    type="text"
                    placeholder="What are you looking for?"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onFocus={() => {
                        setIsQueryFocused(true);
                        setIsLocationFocused(false);
                    }}
                    className="outline-none bg-transparent w-full text-gray-700 placeholder-gray-400"
                />
                {/* Suggestions for query */}
                {isQueryFocused && (query.trim() === "" || filteredQuerySuggestions.length > 0) && (
                    <SuggestionsContainer>
                        {query.trim() === "" ? (
                            <>
                                {/* Query History */}
                                {searchHistory.queries.map((historyQuery, idx) => (
                                    <div
                                        key={idx}
                                        className="hover:bg-brand-gray-600 p-3 rounded-xl mx-4 cursor-pointer flex items-center text-lg"
                                        onClick={() => {
                                            setQuery(historyQuery);
                                            setIsQueryFocused(false);
                                        }}
                                    >
                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center mr-4">
                                            <Clock className="text-brand-gray-1005" size={20} />
                                        </span>
                                        <span className="text-black">{historyQuery}</span>
                                    </div>
                                ))}
                            </>
                        ) : (
                            <>
                                {filteredQuerySuggestions.map((title, index) => (
                                    <div
                                        key={index}
                                        className="hover:bg-brand-gray-600 p-3 rounded-xl mx-4 cursor-pointer flex items-center  text-lg"
                                        onClick={() => {
                                            setQuery(title);
                                            setIsQueryFocused(false);
                                        }}
                                    >
                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center mr-4">
                                            <SearchIcon className="text-brand-gray-1005" size={20} />
                                        </span>
                                        {renderHighlighted(title, query)}
                                    </div>
                                ))}
                            </>
                        )}
                    </SuggestionsContainer>
                )}
            </div>

            {/* Search button */}
            <button
                type="submit"
                className="ml-0 sm:ml-4 mt-2 sm:mt-0 bg-blue-400 hover:bg-blue-500 text-white font-semibold rounded-full px-6 py-4 flex items-center transition-colors cursor-pointer"
            >
                <SearchIcon className="mr-2" size={20} />
                Search
            </button>
        </form>
    );
};

export default SearchBar; 