"use client"

import type React from "react"

import { useState } from "react"

interface RangeSliderProps {
    min?: number
    max?: number
    defaultValue?: number
    step?: number
    unit?: string
    onChange?: (value: number) => void
}

export default function RangeSlider({
    min = 0,
    max = 100,
    defaultValue = 30,
    step = 1,
    unit = "mi",
    onChange,
}: RangeSliderProps) {
    const [value, setValue] = useState(defaultValue)

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = Number.parseInt(e.target.value)
        setValue(newValue)
        onChange?.(newValue)
    }

    const percentage = ((value - min) / (max - min)) * 100

    return (
        <div className="w-full ">
            <div className="relative">
                {/* Custom slider container */}
                <div className="relative h-1 bg-gray-200 rounded-full">
                    {/* Filled track */}
                    <div className="absolute h-full bg-gray-800 rounded-full" style={{ width: `${percentage}%` }} />

                    {/* Slider thumb */}
                    <div
                        className="absolute top-1/2 w-[75px] h-[45px] bg-white border-2 text-brand-gray-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 cursor-pointer shadow-sm flex items-center justify-center"
                        style={{ left: `${percentage}%` }}
                    >
                        <div
                            className="text-black opacity-50 tracking-[1%]"
                            style={{ left: `${percentage}%` }}
                        >
                            {value} {unit}
                        </div>
                    </div>

                    {/* Hidden input */}
                    <input
                        type="range"
                        min={min}
                        max={max}
                        step={step}
                        value={value}
                        onChange={handleChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                </div>
            </div>
        </div>
    )
}
