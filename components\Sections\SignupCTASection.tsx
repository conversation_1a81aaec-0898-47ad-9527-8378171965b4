import Image from "next/image";
import SecondaryButton from "../Buttons/secondary-button";

interface SignupCTASectionProps {
    imageSrc: string;
    imageAlt?: string;
}

export default function SignupCTASection({
    imageSrc,
    imageAlt = "YouHook application preview",
}: SignupCTASectionProps) {
    return (
        <section className="relative mt-40">
            <div className="absolute  left-0 w-full top-1/2 -translate-y-1/2">
                <div className="container mx-auto">
                    <div className="w-1/2">
                        <p className="text-brand-200 text-3xl font-light mb-4 leading-tight">
                            Ready to join?
                        </p>
                        <h2 className="text-4xl md:text-7xl font-medium text-brand-600 mb-8 ">
                            Sign up today and start earning
                        </h2>
                        {/* Description lines */}
                        <div className="mb-20">
                            <p className="text-brand-200 text-xl mb-4">
                                <span className="font-semibold text-brand-600">Post</span> anything you
                                want for free and get paid.
                            </p>
                            <p className="text-brand-200 text-xl ">
                                Or <span className="font-semibold text-brand-600">rent</span> anything you
                                need — faster, easier, and <br /> safer — while helping sustain the ecosystem.
                            </p>
                        </div>
                        <SecondaryButton btnText="Join YouHook" href="#" />
                    </div>
                </div>

            </div>
            <div className="flex justify-end items-center">
                <Image
                    src={imageSrc}
                    alt={imageAlt}
                    width={706.22}
                    height={4061}
                />
            </div>
        </section>
    );
} 