import Image from "next/image";
import { GreenerEconomyIcon } from "../Icons";

interface GreenerEconomySectionProps {
    /** Relative path to the illustration image (e.g. "/images/eco-planet.png") */
    imageSrc: string;
    imageAlt?: string;
}

export default function GreenerEconomySection({
    imageSrc,
    imageAlt = "Greener economy illustration",
}: GreenerEconomySectionProps) {
    return (
        <section className="w-full mt-40 bg-white">
            <div className="container shadow-customBox mx-auto rounded-2xl">
                <div className="bg-white rounded-2xl px-20 py-12 grid grid-cols-1 md:grid-cols-3 gap-10 items-center">
                    {/* Copy */}
                    <div className="col-span-2">
                        <h2 className="text-3xl md:text-5xl font-semibold text-brand-600 mb-5 leading-snug">
                            Building a {" "}
                            <span className="text-brand-green relative">
                                Greener Economy
                                <span className="absolute bottom-0 left-0">
                                    <GreenerEconomyIcon />
                                </span>
                            </span>
                        </h2>

                        <p className="text-brand-200 text-lg md:text-xl leading-relaxed">
                            YouHook will help <span className="font-semibold text-brand-600">reduce waste</span> while allowing people to <br />
                            earn from what they have, creating a more sustainable economy.
                        </p>

                        {/* Illustration */}
                    </div>
                    <div className="col-span-1">
                        <Image
                            src={imageSrc}
                            alt={imageAlt}
                            width={243}
                            height={243}
                            className="w-full h-auto select-none"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
} 