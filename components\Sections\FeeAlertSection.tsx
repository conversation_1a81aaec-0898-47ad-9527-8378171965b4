
import { AlertTriangleIcon } from "../Icons";
import SecondaryButton from "../Buttons/secondary-button";

export default function FeeAlertSection() {
    return (
        <section className="w-full bg-white mt-40">
            <div className="container mx-auto text-center">
                {/* Eyebrow */}
                <p className="text-brand-200 mb-2 text-xl md:text-2xl font-medium">
                    Secure your YouHook account!
                </p>

                {/* Heading */}
                <div className=" mb-6 flex items-center justify-center gap-x-3">
                    <span>
                        <AlertTriangleIcon />
                    </span>
                    <h2 className="text-brand-600 text-4xl md:text-6xl font-semibold">Fees will be added soon</h2>
                </div>

                {/* Supporting copy */}
                <p className="text-brand-100 text-lg md:text-xl mb-6 leading-snug mx-auto font-medium">
                    A 15%–18% service fee will soon apply to all accounts <br className="hidden md:block" /> that haven’t signed up in time.
                </p>
                <p className="text-brand-100 text-lg md:text-xl mb-6 leading-snug mx-auto font-regulars">
                    Everyone joining now keeps 100% of their earnings for life.
                </p>
                <p className="text-brand-100 text-lg md:text-xl mb-12 leading-snug mx-auto font-medium">
                    Sign up before fees are added
                </p>

                {/* CTA */}
                <SecondaryButton btnText="Join YouHook" href="#" />
            </div>
        </section>
    );
} 