# Font Files Directory

## Made Tommy Soft Font Files

Please download the "Made Tommy Soft" font files and place them in this directory with the following naming convention:

### Required Files:
- `made-tommy-soft-thin.woff2` (weight: 100)
- `made-tommy-soft-light.woff2` (weight: 300)
- `made-tommy-soft-regular.woff2` (weight: 400)
- `made-tommy-soft-medium.woff2` (weight: 500)
- `made-tommy-soft-bold.woff2` (weight: 700)
- `made-tommy-soft-black.woff2` (weight: 900)

### Alternative Formats (optional):
If .woff2 files are not available, you can also use:
- `.woff` files
- `.ttf` files

### Notes:
- The font configuration is set up in `app/fonts/made-tommy-soft.ts`
- If you have different font file names, update the paths in the font configuration file
- If you don't have all weights, you can remove the unused weight entries from the configuration

### Font Usage:
Once the files are placed here, the font will be automatically available as:
- Default font for all text elements
- Tailwind class: `font-made-tommy` or `font-sans`
- CSS variable: `var(--font-made-tommy-soft)` 