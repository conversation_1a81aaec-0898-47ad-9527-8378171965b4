import { Check } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import SecondaryButton from "../Buttons/secondary-button";

export default function EverythingNeedSection() {
    return (
        <section className="w-full bg-white mt-40">
            <div className="container mx-auto ">
                <div className="text-center mb-4">
                    <p className="text-brand-100 text-3xl font-light">Things you’ll get access to.</p>
                    <h2 className="text-brand-600 font-medium text-7xl">Everything you  need <br className="hidden md:block" /> to know</h2>
                </div>
            </div>
            <div className="relative pt-20">
                <div className="absolute left-1/2 -translate-x-1/2 top-0 h-full w-[38px]">
                    <Image
                        src="/images/line-img.png"
                        alt="line"
                        fill
                        // className="object-contain"
                        sizes="38px"
                    />
                </div>
                <div className="grid grid-cols-2">
                    <div>
                        <Image src="/images/everything-img-1.png" alt="Everything need" width={722.23} height={530.31} />
                    </div>
                    <div className="w-[60%] pl-[60px]">
                        <h3 className="font-bold text-brand-600 text-4xl leading-14 mb-6">
                            Every rental is safe and protected.
                        </h3>
                        <ul className="space-y-4">
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    <span className="font-medium text-brand-600">$2,000 coverage</span>  on every rental—no exceptions. <Link href="#" className="font-regular underline">Learn more.</Link>
                                </p>
                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    <span className="font-medium text-brand-600">Damage? Theft?</span>  We’ve got you covered.
                                </p>

                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    Real time
                                    <span className="font-medium text-brand-600"> rental tracking</span>  with your rental manger
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className="grid grid-cols-2">
                    <div>
                        <Image src="/images/everything-img-2.png" alt="Everything need" width={722.23} height={530.31} />
                    </div>
                    <div className="w-[60%] pl-[60px]">
                        <h3 className="font-bold text-brand-600 text-4xl leading-14 mb-6">
                            Full access to your security deposits and payouts
                        </h3>
                        <ul className="space-y-4">
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    <span className="font-medium text-brand-600">Same-day</span>  payout initiations.
                                </p>
                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    Security deposits you can control.
                                </p>

                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    <span className="font-medium text-brand-600"> 0% fees</span>  Yes, zero!
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className="grid grid-cols-2">
                    <div>
                        <Image src="/images/everything-img-3.png" alt="Everything need" width={722.23} height={530.31} />
                    </div>
                    <div className="w-[60%] pl-[60px]">
                        <h3 className="font-bold text-brand-600 text-4xl leading-14 mb-6">
                            Designed to manage more rentals with less work
                        </h3>
                        <ul className="space-y-4">
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    Access
                                    <span className="font-medium text-brand-600"> 20+ management tools </span>
                                    with <span className="text-brand-blue-50">Ai</span>  integration
                                </p>
                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    Quickly start and end your rentals.
                                </p>
                            </li>
                            <li className="flex  gap-3">
                                <span className="mt-1"><Check className="w-6 h-6 text-brand-600" /></span>
                                <p className=" font-light text-brand-200 text-2xl">
                                    Customize your   <span className="font-medium text-brand-600"> calendar </span>  to suit your availability
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="text-center mt-6">
                <SecondaryButton btnText="Join YouHook" href="#" />
            </div>
        </section>
    );
}