import { useState } from "react"
import { Check } from "lucide-react"

// Camera icon component removed as it's not used

interface IDVerificationScreenProps {
    onContinue: (verificationType: "upload" | "camera") => void
    onSkip: () => void
    onBack?: () => void
}

export default function IDVerificationScreen({ onContinue, onSkip }: IDVerificationScreenProps) {
    const [selectedOption, setSelectedOption] = useState<"upload" | "camera" | null>(null)

    const handleOptionSelect = (option: "upload" | "camera") => {
        setSelectedOption(option)
    }

    const handleContinue = () => {
        if (selectedOption) {
            onContinue(selectedOption)
        }
    }

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-center text-2xl font-medium mb-8">
                    Lets get you verified!
                </h2>

                <h3 className="text-brand-black-300 text-xl font-medium mb-6">
                    Add your government ID
                </h3>

                <p className="text-brand-black-300 text-lg mb-4">
                    By verifying your government ID we can make sure it really you,
                    keeping our platform safe.
                </p>

                <div className="mb-8">
                    {/* Upload existing photo option */}
                    <div
                        onClick={() => handleOptionSelect("upload")}
                        className={`flex items-center justify-between py-6 border-b cursor-pointer transition-colors border-b-brand-gray-1003`}
                    >
                        <span className="text-brand-black-300 text-lg font-medium">Upload an existing photo</span>

                        {
                            selectedOption === "upload" ? (
                                <div className="w-[25px] h-[25px] flex items-center justify-center rounded-full border border-brand-gray-1003 bg-black">
                                    <Check className="text-white w-4 h-4" />
                                </div>
                            ) : (
                                <div className="w-[25px] h-[25px] flex rounded-full border border-brand-gray-1003 bg-white" />
                            )
                        }
                    </div>

                    {/* Take a photo option */}
                    <div
                        onClick={() => handleOptionSelect("camera")}
                        className={`flex items-center justify-between py-6 border-b cursor-pointer transition-colors border-b-brand-gray-1003`}
                    >
                        <span className="text-brand-black-300 text-lg font-medium">Take a photo</span>

                        {
                            selectedOption === "camera" ? (
                                <div className="w-[25px] h-[25px] flex items-center justify-center rounded-full border border-brand-gray-1003 bg-black">
                                    <Check className="text-white w-4 h-4" />
                                </div>
                            ) : (
                                <div className="w-[25px] h-[25px] flex rounded-full border border-brand-gray-1003 bg-white" />
                            )
                        }
                    </div>
                </div>

                {/* Privacy notice */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8 text-left">
                    <h4 className="font-medium text-brand-black-300 mb-2">Your Privacy</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                        We take your privacy seriously. Your ID and data will be kept safe, secure
                        and will never be shared with anyone. <span className="underline cursor-pointer">Learn more.</span>
                    </p>
                </div>

                <div className="space-y-4 flex flex-col justify-center items-center">
                    <button
                        onClick={handleContinue}
                        disabled={!selectedOption}
                        className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors ${selectedOption
                            ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                            : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        Continue
                    </button>

                    <button
                        onClick={onSkip}
                        className="text-brand-black-200 text-lg font-medium underline hover:text-brand-black-400 transition-colors cursor-pointer"
                    >
                        Skip for now
                    </button>
                </div>
            </div>
        </div>
    )
} 