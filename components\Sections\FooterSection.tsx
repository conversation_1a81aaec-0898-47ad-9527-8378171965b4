import Image from "next/image";
import Link from "next/link";
import {
    InstagramIcon,
    FacebookIcon,
    XIcon,
    YoutubeIcon,
    TikTokIcon,
} from "../Icons";

export default function FooterSection() {
    return (
        <footer className="relative w-full pt-20  border-t border-brand-700/30 bg-brand-1100">
            <div className="container mx-auto grid grid-cols-1 md:grid-cols-[repeat(5,_minmax(0,_1fr))] gap-12">
                {/* Column groups */}
                <div className="space-y-3">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">Policy</h4>
                    <ul className="space-y-2 text-brand-200 text-lg">
                        <li><Link href="#">Terms of Service</Link></li>
                        <li><Link href="#">Privacy Policy</Link></li>
                        <li><Link href="#">Community Guidelines</Link></li>
                    </ul>
                </div>
                <div className="space-y-3">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">Help</h4>
                    <ul className="space-y-2 text-brand-200 text-lg">
                        <li><Link href="#">Help center</Link></li>
                        <li><Link href="#">Contact us</Link></li>
                        <li><Link href="#">File a claim</Link></li>
                    </ul>
                </div>
                <div className="space-y-3">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">Rent</h4>
                    <ul className="space-y-2 text-brand-200 text-lg">
                        <li><Link href="#">Create listing</Link></li>
                        <li><Link href="#">Rental manger</Link></li>
                    </ul>
                </div>
                <div className="space-y-3">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">About us</h4>
                    <ul className="space-y-2 text-brand-200 text-lg">
                        <li><Link href="#">About YouHook</Link></li>
                        <li><Link href="#">Affiliate &amp; Jobs</Link></li>
                        <li><Link href="#">What is YouHook?</Link></li>
                    </ul>
                </div>
                {/* Socials */}
                <div className="space-y-3 md:col-span-1">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">Socials</h4>
                    <p className="text-brand-200 text-lg leading-snug ">
                        A peer to peer market place for renting anything.
                    </p>
                    <div className="flex items-center gap-3 mt-2 text-brand-600">
                        <Link href="#" aria-label="TikTok"><TikTokIcon /></Link>
                        <Link href="#" aria-label="Instagram"><InstagramIcon /></Link>
                        <Link href="#" aria-label="Facebook"><FacebookIcon /></Link>
                        <Link href="#" aria-label="X"><XIcon /></Link>
                        <Link href="#" aria-label="YouTube"><YoutubeIcon /></Link>
                    </div>
                </div>
                {/* App badges */}
                <div className="space-y-4">
                    <h4 className="font-medium text-lg text-brand-600 mb-2">Coming soon</h4>
                    <div className="flex flex-col gap-3 w-32">
                        <Image src="/images/google-play-badge.png" alt="Get it on Google Play" width={128} height={38} />
                        <Image src="/images/app-store-badge.png" alt="Download on the App Store" width={128} height={38} />
                    </div>
                </div>
            </div>

            {/* Bottom bar */}
            <div className="container mx-auto mt-12 flex justify-between items-center">
                <div className="w-[480px] border-t border-brand-1700 pb-4">
                    {/* Copyright */}
                    <div className="text-brand-600 mb-10 pt-8">
                        <p className="mb-2">©2025 Youhook. All rights reserved.</p>
                        <p>Youhook v1.21</p>
                    </div>

                    {/* Language + currency */}
                    <div className="flex  gap-8 font-medium">
                        <button className="flex items-center gap-2 text-brand-600">
                            <Image src="/images/flag-us.png" alt="English" width={16} height={16} />
                            English(US)
                        </button>
                        <button className="text-brand-600"> $USD</button>
                        <Image src="/images/footer-ai.png" alt="English" width={30} height={26} />
                    </div>
                </div>
                <div className="ml-14 self-end">
                    <Image src="/images/YouHook.png" alt="Youhook" width={2138} height={532} />
                </div>
            </div>
        </footer>
    );
} 