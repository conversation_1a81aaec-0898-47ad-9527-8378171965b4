"use client"

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { FilterIcon } from "../Icons"
import { X, ChevronLeft, Calendar, Clock } from "lucide-react"
import FiltersTabs from "./FiltersTabs"
import PriceRangeSlider from "./PriceRangeSlider"
import { useState } from "react"
import QuantitySelector from "./QuantitySelector"

export default function FiltersModal() {
    const [selectedFilter, setSelectedFilter] = useState<string | null>(null)
    const [activeTab, setActiveTab] = useState<string>("localDelivery")

    const filterOptions: Record<string, string[]> = {
        "Sort by": [
            "Suggested",
            "Distance: Nearest first",
            "Rental: Most rented",
            "Price: Lowest first",
            "Price: Highest first",
            "Rating: Highest owner rating first",
            "Rating: Highest item rating first",
        ],
        "Price": ["Any", "$0 - $50", "$50 - $100", "$100+"],
        "Category": ["Any", "Spaces", "Events", "Adventure", "Attire", "Production", "Tools", "Sports", "Electronics", "Kids & Babies", "School Supplies", "Furniture", "Instruments & Music", "Landscape", "Medical Supplies", "Other"],
        "Rental rate": ["Hour + Day", "By the day", "By the hour"],
        "Quantity available": ["1 or more", "5 or more"],
    }

    // store chosen values per tab
    type TabValues = Record<string, Record<string, string>>;
    const [selectedValues, setSelectedValues] = useState<TabValues>({ localDelivery: {}, localPickup: {} })

    const tabValues = selectedValues[activeTab] ?? {}

    const setTabValue = (filterName: string, value: string) => {
        setSelectedValues((prev) => ({
            ...prev,
            [activeTab]: {
                ...(prev[activeTab] ?? {}),
                [filterName]: value,
            },
        }))
    }

    const handleOptionSelect = (filterName: string, option: string) => {
        setTabValue(filterName, option)
    }

    return (

        <Dialog>
            <DialogTrigger className="flex items-center gap-2 border border-brand-gray-450 rounded-2xl px-4 py-2 cursor-pointer">
                <FilterIcon /> Filters
            </DialogTrigger>
            <DialogContent
                onInteractOutside={(e) => e.preventDefault()}
                onEscapeKeyDown={(e) => e.preventDefault()}
                className="filters-modal-content px-0 min-w-[650px] shadow-custom-2  pb-0">
                <DialogHeader
                    className="flex flex-row items-center justify-between border-b border-black border-opacity-[0.9] pb-4 px-8">
                    {selectedFilter ? (
                        <ChevronLeft className="cursor-pointer" onClick={() => setSelectedFilter(null)} />
                    ) : (
                        <DialogClose asChild>
                            <X className="cursor-pointer" />

                        </DialogClose>
                    )}
                    <DialogTitle className="text-brand-100 text-xl font-medium text-center">
                        {selectedFilter ?? "Filters"}
                    </DialogTitle>
                    <div></div>
                </DialogHeader>
                <div className={`${selectedFilter === "Price" || selectedFilter === "Rental rate" || selectedFilter === "Quantity available" ? "h-[200px]" : "h-[400px]"} overflow-y-auto no-scroll`}>
                    {selectedFilter ? (
                        selectedFilter === "Price" ? (
                            <div className="px-8 py-6">
                                <p className="text-brand-100 text-xl font-medium mb-14">Set price</p>
                                <PriceRangeSlider
                                    min={1}
                                    max={1000}
                                    initialMinValue={1}
                                    initialMaxValue={1000}
                                    step={1}
                                    onChange={({ min, max }) =>
                                        setTabValue(
                                            "Price",
                                            min === 1 && max === 1000 ? "Any" : `$${min} - $${max}`
                                        )
                                    }
                                />
                            </div>
                        ) : selectedFilter === "Rental rate" ? (
                            <div className="px-8 py-6">
                                <p className="text-brand-100 text-xl font-medium mb-6">Set rate</p>
                                <div className="flex gap-4 border border-brand-gray-450 rounded-xl p-1">
                                    {filterOptions[selectedFilter]?.map((opt) => {
                                        const isSelected = tabValues[selectedFilter] === opt
                                        const radioId = `${selectedFilter}-${opt}`
                                        return (
                                            <label
                                                key={opt}
                                                htmlFor={radioId}
                                                className={`flex items-center gap-2 justify-center py-3 rounded-xl border-2 text-black hover:bg-brand-gray-750 font-medium cursor-pointer w-full ${isSelected ? "border-black bg-brand-gray-750" : "border-transparent"}`}
                                            >
                                                {/* Hidden radio */}
                                                <input
                                                    type="radio"
                                                    id={radioId}
                                                    name={selectedFilter}
                                                    value={opt}
                                                    className="sr-only"
                                                    checked={isSelected}
                                                    onChange={() => handleOptionSelect(selectedFilter, opt)}
                                                />
                                                {opt === "By the day" && <Calendar className="w-4 h-4" />}
                                                {opt === "By the hour" && <Clock className="w-4 h-4" />}
                                                {opt}
                                            </label>
                                        )
                                    })}
                                </div>
                            </div>
                        ) : selectedFilter === "Quantity available" ? (
                            <QuantitySelector
                                value={Number(tabValues["Quantity available"] ?? 1)}
                                onChange={(val: number) => setTabValue("Quantity available", String(val))}
                            />
                        ) : (
                            <div className="py-6 flex flex-col gap-2">
                                {filterOptions[selectedFilter]?.map((opt) => {
                                    const isSelected = tabValues[selectedFilter] === opt
                                    const radioId = `${selectedFilter}-${opt}`
                                    return (
                                        <label
                                            key={opt}
                                            htmlFor={radioId}
                                            className={`px-8 flex justify-between items-center cursor-pointer py-3  border-brand-black-150 hover:bg-brand-gray-700`}
                                        >
                                            <span className="text-brand-100 font-medium text-lg select-none">{opt}</span>

                                            {/* Hidden radio input */}
                                            <input
                                                type="radio"
                                                id={radioId}
                                                name={selectedFilter}
                                                value={opt}
                                                className="sr-only peer"
                                                checked={isSelected}
                                                onChange={() => handleOptionSelect(selectedFilter, opt)}
                                            />

                                            {/* Custom radio visual */}
                                            <span className="w-[26px] h-[26px] rounded-full border border-brand-100 flex items-center justify-center peer-checked:border-brand-100 peer-checked:bg-brand-600 transition-colors peer-hover:bg-brand-300 peer-hover:border-brand-300">
                                                <span className="w-[12px] h-[12px] bg-white rounded-full" />
                                            </span>
                                        </label>
                                    )
                                })}
                            </div>
                        )
                    ) : (
                        <FiltersTabs
                            onSelectFilter={setSelectedFilter}
                            selectedValues={tabValues}
                            activeTab={activeTab}
                            onTabChange={setActiveTab}
                        />
                    )}
                </div>
                <DialogFooter className="flex sm:justify-between flex-row px-8 bg-white rounded-b-2xl py-4">
                    <button className="underline text-brand-600 cursor-pointer">Rest all</button>
                    <button className="bg-brand-600 text-white rounded-full px-10 py-3 cursor-pointer">Apply filter</button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
