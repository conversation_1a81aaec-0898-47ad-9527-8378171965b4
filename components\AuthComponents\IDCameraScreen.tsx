import { useState, useRef, useEffect, useCallback } from "react"
import { Camera, Lock } from "lucide-react"

interface IDCameraScreenProps {
    idType: "drivers-license" | "passport" | "identity-card"
    onContinue: (capturedImage: string) => void
    onComplete?: () => void
    onBack?: () => void
    onRetake?: () => void
    capturedImage?: string | null
    isBackSide?: boolean
}

export default function IDCameraScreen({ idType, onContinue, onComplete, capturedImage: externalCapturedImage, isBackSide = false }: IDCameraScreenProps) {
    const [capturedImage, setCapturedImage] = useState<string | null>(externalCapturedImage || null)
    const [isCameraActive, setIsCameraActive] = useState(false)
    const [cameraError, setCameraError] = useState<string | null>(null)

    const videoRef = useRef<HTMLVideoElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const streamRef = useRef<MediaStream | null>(null)
    const prevCapturedImageRef = useRef<string | null | undefined>(externalCapturedImage)

    const getIDTypeDisplay = () => {
        switch (idType) {
            case "drivers-license":
                return "driver's license"
            case "passport":
                return "passport"
            case "identity-card":
                return "identity card"
            default:
                return "ID"
        }
    }

    const getInstructions = () => {
        if (isBackSide) {
            switch (idType) {
                case "drivers-license":
                    return "Fit the back side of your ID within the frame, make sure to have good lighting."
                case "identity-card":
                    return "Fit the back side of your ID within the frame, make sure to have good lighting."
                default:
                    return "Fit the back side of your ID within the frame, make sure to have good lighting."
            }
        } else {
            switch (idType) {
                case "drivers-license":
                    return "Fit the front side of your ID within the frame, make sure to have good lighting."
                case "passport":
                    return "Fit your passport within the frame, make sure to have good lighting."
                case "identity-card":
                    return "Fit the front side of your ID within the frame, make sure to have good lighting."
                default:
                    return "Fit your ID within the frame, make sure to have good lighting."
            }
        }
    }

    // Initialize camera
    const startCamera = useCallback(async () => {
        if (isCameraActive) {
            return
        }

        try {
            setCameraError(null)

            // First try with back camera, fallback to any camera
            let stream;
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                })
            } catch {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                })
            }

            if (videoRef.current && stream) {
                const videoEl = videoRef.current
                videoEl.srcObject = stream
                streamRef.current = stream
                videoEl.play().catch(() => { })
                setIsCameraActive(true)

                const handlePlaying = () => {
                    setIsCameraActive(true)
                    videoEl.removeEventListener('playing', handlePlaying)
                }
                videoEl.addEventListener('playing', handlePlaying)
            }
        } catch {
            setCameraError('Unable to access camera. Please ensure you have granted camera permissions and try refreshing the page.')
        }
    }, [isCameraActive])

    // Stop camera
    const stopCamera = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop())
            streamRef.current = null
        }
        setIsCameraActive(false)
    }

    // Capture photo from video stream
    const handleTakePhoto = () => {
        if (videoRef.current && canvasRef.current) {
            const canvas = canvasRef.current
            const video = videoRef.current
            const context = canvas.getContext('2d')

            if (context) {
                // Set canvas size to match video
                canvas.width = video.videoWidth
                canvas.height = video.videoHeight

                // Draw current video frame to canvas
                context.drawImage(video, 0, 0, canvas.width, canvas.height)

                // Convert canvas to base64 image
                const imageData = canvas.toDataURL('image/jpeg', 0.8)
                setCapturedImage(imageData)
                onContinue(imageData)

                // Stop camera after capturing
                stopCamera()
            }
        }
    }

    // Start camera when component mounts
    useEffect(() => {
        if (!capturedImage) {
            startCamera()
        }

        // Cleanup: stop camera when component unmounts
        return () => {
            stopCamera()
        }
    }, [capturedImage, startCamera])

    // Update local state when external captured image changes
    useEffect(() => {
        const newCapturedImage = externalCapturedImage || null
        const prevCapturedImage = prevCapturedImageRef.current

        setCapturedImage(newCapturedImage)

        // If external image was cleared (went from having image to null), restart camera
        if (prevCapturedImage && !newCapturedImage) {
            startCamera()
        }

        // Update ref for next comparison
        prevCapturedImageRef.current = externalCapturedImage
    }, [externalCapturedImage, startCamera])

    // Handle retake photo (unused but kept for potential future use)
    // const handleRetakePhoto = () => {
    //     setCapturedImage(null)
    //     startCamera()
    // }



    const canContinue = capturedImage !== null

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-4">
                    {capturedImage
                        ? `Is the ${isBackSide ? 'back' : 'front'} of your ${getIDTypeDisplay()} clear?`
                        : `Take a photo of the ${isBackSide ? 'back' : 'front'} of your ${getIDTypeDisplay()}`
                    }
                </h2>

                <p className="text-brand-black-300 text-base mb-8">
                    {capturedImage ? "Make sure it is well-lit, clear and nothing is out of frame." : getInstructions()}
                </p>



                {/* Camera Viewfinder Area */}
                <div className="flex justify-center">
                    <div className="relative w-full ">
                        {capturedImage ? (
                            // Show captured image
                            <div className="relative w-full h-[335px] flex items-center justify-center rounded-lg overflow-hidden">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                    src={capturedImage}
                                    alt="Captured ID"
                                    className="h-full w-full object-cover rounded-lg"
                                />
                            </div>
                        ) : (
                            // Live camera viewfinder
                            <div className="relative">
                                <div className="w-full h-[335px] flex items-center justify-center bg-black rounded-lg overflow-hidden relative">
                                    {/* Always render video element */}
                                    <video
                                        ref={videoRef}
                                        autoPlay
                                        playsInline
                                        muted
                                        className={`w-full h-full object-cover ${isCameraActive ? 'block' : 'hidden'}`}
                                    />

                                    {/* Document positioning frame overlay */}
                                    {isCameraActive && (
                                        <div className="absolute inset-0 pointer-events-none">
                                            {/* Document frame - clear area */}
                                            <div className="absolute inset-0 flex items-center justify-center">
                                                <div
                                                    className="w-[90%] h-[90%] relative"
                                                    style={{
                                                        boxShadow: '0 0 0 1000px rgba(0, 0, 0, 0.3)' // Red overlay outside the frame
                                                    }}
                                                >
                                                    {/* Document frame border */}
                                                    <div
                                                        className="absolute inset-0 border-white rounded-lg"
                                                        style={{ borderWidth: '3px' }}
                                                    >

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Overlay content when video is not active */}
                                    {!isCameraActive && (
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            {cameraError ? (
                                                // Show error message
                                                <div className="text-center text-white p-4">
                                                    <Camera size={48} className="mx-auto mb-4 text-gray-400" />
                                                    <p className="text-sm">{cameraError}</p>
                                                    <button
                                                        onClick={startCamera}
                                                        className="mt-4 px-4 py-2 bg-brand-blue-200 text-white rounded-lg text-sm"
                                                    >
                                                        Try Again
                                                    </button>
                                                </div>
                                            ) : (
                                                // Loading state
                                                <div className="text-center text-white">
                                                    <Camera size={48} className="mx-auto mb-4 text-gray-400" />
                                                    <p className="text-sm">Starting camera...</p>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>

                                {/* Hidden canvas for capturing photos */}
                                <canvas
                                    ref={canvasRef}
                                    style={{ display: 'none' }}
                                />
                            </div>
                        )}
                    </div>
                </div>
                <div className={`${capturedImage ? 'bg-brand-transparent' : 'bg-brand-gray-1003'} w-full h-[1px]  my-6`} />
                {/* Manual Start Camera Button */}
                {!isCameraActive && !cameraError && !capturedImage && (
                    <div className="mb-8">
                        <button
                            onClick={startCamera}
                            className="w-full h-[60px] bg-brand-blue-200 text-white rounded-lg text-lg font-medium cursor-pointer hover:bg-brand-blue-300 transition-colors flex items-center justify-center gap-2"
                        >
                            <Camera size={20} />
                            Start Camera
                        </button>
                    </div>
                )}

                {/* Take Photo Button */}
                {!capturedImage && isCameraActive && !cameraError && (
                    <div className="mb-8">
                        <button
                            onClick={handleTakePhoto}
                            className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium cursor-pointer hover:bg-brand-black-300 transition-colors flex items-center justify-center gap-2"
                        >
                            <Camera size={20} />
                            Take photo
                        </button>
                    </div>
                )}

                {/* Tip Section - only show when no captured image */}
                {!capturedImage && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8">
                        <h4 className="font-medium text-brand-black-300 mb-2">Tip for success</h4>
                        <p className="text-gray-600 text-sm">
                            Place ID far enough away so it&apos;s not blurry. Be sure that your thumbs don&apos;t cover any important information
                        </p>
                    </div>
                )}

                {/* Continue Button */}
                {capturedImage && (
                    <div className="flex justify-center">
                        <button
                            onClick={() => onComplete?.()}
                            disabled={!canContinue}
                            className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 ${canContinue
                                ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                                : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                                }`}
                        >
                            <Lock size={20} /> {(isBackSide || idType === "passport") ? 'Submit & continue' : 'Continue'}
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
} 