"use client";

import { ReactNode, useState } from "react";
import { Heart, Star, TruckIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface PrimaryCardProps {
    image: string;
    price: number;
    priceUnit?: string;
    rating: number;
    title: string;
    location: string;
    distance?: string;
    onFavoriteClick?: (isFavorited: boolean) => void;
    deliveryIncluded?: boolean;
    className?: string;
    children?: ReactNode;
    link?: string;
}

// Rating Star Component
const RatingStar = ({ rating }: { rating: number }) => {
    if (rating === 0) {
        // Gray star for 0 rating
        return (
            <Star size={16} className="fill-gray-400 text-gray-400" />
        );
    } else if (rating < 4.0) {
        // Half yellow, half gray star for ratings less than 4.0
        return (
            <div className="relative">
                <Star size={16} className="fill-gray-400 text-gray-400" />
                <div className="absolute inset-0 overflow-hidden w-1/2">
                    <Star size={16} className="fill-brand-500 text-brand-500" />
                </div>
            </div>
        );
    } else {
        // Full yellow star for 4.0 and above
        return (
            <Star size={16} className="fill-brand-500 text-brand-500" />
        );
    }
};

export function PrimaryCard({
    image,
    price,
    priceUnit = "day",
    rating,
    title,
    location,
    distance,
    onFavoriteClick,
    className,
    children,
    deliveryIncluded = false,
    link = "#",
}: PrimaryCardProps) {
    const [isFavorited, setIsFavorited] = useState(false);

    const handleFavoriteClick = () => {
        const newFavoritedState = !isFavorited;
        setIsFavorited(newFavoritedState);
        onFavoriteClick?.(newFavoritedState);
    };
    return (
        <Link href={link} className={cn("bg-white rounded-lg block", className)}>
            {/* Image Section */}
            <div className="relative">
                <Image
                    src={image}
                    alt={title}
                    className="w-full h-[315px] object-cover rounded-lg"
                    width={325}
                    height={315}
                />

                {/* Favorite Button */}
                <button
                    onClick={handleFavoriteClick}
                    className="absolute top-3 right-3 cursor-pointer"
                >
                    <Heart
                        size={28}
                        className={cn(
                            "transition-colors",
                            isFavorited ? "fill-brand-800 text-white" : "text-white fill-brand-200"
                        )}
                    />
                </button>
                {
                    deliveryIncluded && (
                        <div className="absolute bottom-3 left-3 py-2 px-6 rounded-full backdrop-blur-2xl bg-white  border border-brand-700 transition-colors">
                            <span className="flex items-center gap-3 text-base text-brand-600">
                                <TruckIcon size={20} className="text-brand-600 w-[20px] h-[20px]" />
                                <span className="text-sm  text-brand-600">Delivery included</span>
                            </span>
                        </div>
                    )
                }

            </div>

            {/* Content Section */}
            <div className="py-4 px-1">
                {/* Price */}
                <div className="flex items-center justify-between gap-1">
                    <div className="text-lg font-medium">
                        <span className="text-xl text-black">${price}</span>
                        <span className="text-lg text-brand-300"> / {priceUnit}</span>
                    </div>
                    <div className="flex items-center gap-1 w-[65px] h-[29px]  justify-center bg-brand-400 rounded-full">
                        <RatingStar rating={rating} />
                        <span className="text-base font-medium text-brand-100">
                            {rating === 0 ? "(0)" : rating.toFixed(1)}
                        </span>
                    </div>
                </div>

                {/* Title */}
                <h3 className="text-xl font-normal text-brand-100 mb-1">{title}</h3>

                {/* Location */}
                <div className="text-base text-brand-200 font-normal">
                    {location}
                    {distance && (
                        <>
                            <span className="mx-1">•</span>
                            {distance}
                        </>
                    )}
                </div>

                {/* Additional content */}
                {children}
            </div>
        </Link>
    );
} 