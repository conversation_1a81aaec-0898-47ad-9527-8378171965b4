import { useState } from "react"
import { Check } from "lucide-react"
import CountryCodeDropdown from "./CountryCodeDropdown"

interface IDTypeSelectionScreenProps {
    onContinue: (idType: IDType) => void
    onBack?: () => void
}

type IDType = "drivers-license" | "passport" | "identity-card"

export default function IDTypeSelectionScreen({ onContinue }: IDTypeSelectionScreenProps) {
    const [selectedCountry, setSelectedCountry] = useState({ name: "United States", code: "+1" })
    const [selectedIDType, setSelectedIDType] = useState<IDType | null>(null)

    const handleIDTypeSelect = (type: IDType) => {
        setSelectedIDType(type)
    }

    const handleContinue = () => {
        if (selectedIDType) {
            onContinue(selectedIDType)
        }
    }

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-8">
                    Choose an ID type to submit
                </h2>

                {/* Country Code Dropdown */}
                <div className="mb-8">
                    <div className="border border-brand-gray-960 rounded-lg">
                        <CountryCodeDropdown
                            selected={selectedCountry}
                            onChange={setSelectedCountry}
                        />
                    </div>

                </div>

                {/* ID Type Options */}
                <div className="mb-8">
                    {/* Driver's License */}
                    <div
                        onClick={() => handleIDTypeSelect("drivers-license")}
                        className="flex items-center justify-between py-6 border-b cursor-pointer transition-colors border-b-brand-gray-1003"
                    >
                        <span className="text-brand-black-300 text-lg font-medium">Driver&apos;s license</span>

                        {selectedIDType === "drivers-license" ? (
                            <div className="w-[25px] h-[25px] flex items-center justify-center rounded-full border border-brand-gray-1003 bg-black">
                                <Check className="text-white w-4 h-4" />
                            </div>
                        ) : (
                            <div className="w-[25px] h-[25px] flex rounded-full border border-brand-gray-1003 bg-white" />
                        )}
                    </div>

                    {/* Passport */}
                    <div
                        onClick={() => handleIDTypeSelect("passport")}
                        className="flex items-center justify-between py-6 border-b cursor-pointer transition-colors border-b-brand-gray-1003"
                    >
                        <span className="text-brand-black-300 text-lg font-medium">Passport</span>

                        {selectedIDType === "passport" ? (
                            <div className="w-[25px] h-[25px] flex items-center justify-center rounded-full border border-brand-gray-1003 bg-black">
                                <Check className="text-white w-4 h-4" />
                            </div>
                        ) : (
                            <div className="w-[25px] h-[25px] flex rounded-full border border-brand-gray-1003 bg-white" />
                        )}
                    </div>

                    {/* Identity Card */}
                    <div
                        onClick={() => handleIDTypeSelect("identity-card")}
                        className="flex items-center justify-between py-6 border-b cursor-pointer transition-colors border-b-brand-gray-1003"
                    >
                        <span className="text-brand-black-300 text-lg font-medium">Identity card</span>

                        {selectedIDType === "identity-card" ? (
                            <div className="w-[25px] h-[25px] flex items-center justify-center rounded-full border border-brand-gray-1003 bg-black">
                                <Check className="text-white w-4 h-4" />
                            </div>
                        ) : (
                            <div className="w-[25px] h-[25px] flex rounded-full border border-brand-gray-1003 bg-white" />
                        )}
                    </div>
                </div>

                {/* Privacy Notice */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8 text-left">
                    <h4 className="font-medium text-brand-black-300 mb-2">Your Privacy</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                        We take your privacy seriously. Your ID and data will be kept safe, secure
                        and will never be shared with anyone. <span className="underline cursor-pointer">Learn more.</span>
                    </p>
                </div>

                {/* Continue Button */}
                <div className="flex justify-center">
                    <button
                        onClick={handleContinue}
                        disabled={!selectedIDType}
                        className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors ${selectedIDType
                            ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                            : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        Continue
                    </button>
                </div>
            </div>
        </div>
    )
} 