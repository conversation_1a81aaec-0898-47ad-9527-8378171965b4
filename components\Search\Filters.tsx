
import { ElementType } from "react";

interface FiltersProps {
    Icon: ElementType;
    text: string;
    className?: string;
    isActive?: boolean;
    onClick?: () => void;
    isSearchFilter?: boolean;
}

export default function Filters({
    Icon,
    text,
    className = "",
    isActive = false,
    onClick,
    isSearchFilter = false
}: FiltersProps) {
    const baseClasses = "flex flex-col items-center gap-2 cursor-pointer";
    const searchFilterClasses = isSearchFilter ? "search-filter relative justify-center" : "";
    const activeTextColor = isActive ? "text-brand-600" : "text-brand-gray-400";

    return (
        <div
            className={`${baseClasses} ${searchFilterClasses} ${className}`}
            onClick={onClick}
        >
            <Icon />
            <p className={activeTextColor}>{text}</p>
        </div>
    );
}