@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-made-tommy-soft);
  --font-mono: var(--font-made-tommy-soft);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  /* Custom theme colors */

  --color-brand-100: #202325cc;
  --color-brand-200: #20232599;
  --color-brand-300: #20232566;
  --color-brand-400: #f2f2f2;
  --color-brand-500: #f6b65b;
  --color-brand-600: #202325;
  --color-brand-700: #d8d8d8;
  --color-brand-800: #c0405a;
  --color-brand-900: #00000080;
  --color-brand-1000: #6dbffa;
  --color-brand-1100: #f5f5f5;
  --color-brand-1200: #bcbcbc;
  --color-brand-1300: #f5f5f5a8;
  --color-brand-1400: #2023250d;
  --color-brand-1500: #d9d9d9;
  --color-brand-1600: #656a6a;
  --color-brand-1700: #d4d4d4;
  --color-brand-green: #74c242;
  --color-brand-gray-50: #888888;
  --color-brand-gray-100: #262e3473;
  --color-brand-gray-150: #20232580;
  --color-brand-gray-200: #c7c7c7;
  --color-brand-gray-250: #e9e9e9;
  --color-brand-gray-300: #bbbbbb;
  --color-brand-gray-350: #ededed;
  --color-brand-gray-400: #646464;
  --color-brand-gray-450: #dbdbdb;
  --color-brand-gray-500: #cecece80;
  --color-brand-gray-550: #a2a2a2;
  --color-brand-gray-600: #ebebeb;
  --color-brand-gray-650: #f4f4f4a1;
  --color-brand-gray-660: #f4f4f4;
  --color-brand-gray-700: #f3f3f3;
  --color-brand-gray-750: #f8f8f8;
  --color-brand-gray-800: #f0efef;
  --color-brand-gray-850: #e0e0e0;
  --color-brand-gray-900: #2023251a;
  --color-brand-gray-950: #4d4d4d;
  --color-brand-gray-960: #c6c6c6;
  --color-brand-gray-970: #9a9a9a;
  --color-brand-gray-980: #e1e1e1;
  --color-brand-gray-990: #9a9a9a47;
  --color-brand-gray-995: #b6b6b6;
  --color-brand-gray-1000: #e5e5e3;
  --color-brand-gray-1001: #d3d3d3;
  --color-brand-gray-1002: #6e6e6e;
  --color-brand-gray-1003: #aeaeae;
  --color-brand-gray-1004: #696969;
  --color-brand-gray-1005: #707070;
  --color-brand-gray-1006: #cfcfcf;
  --color-brand-gray-1007: #e3e3e3;
  --color-brand-gray-1008: #f9f9f9;
  --color-brand-gray-1009: #848484;
  --color-brand-gray-1010: #828282;
  --color-brand-gray-1011: #d5d5d5;
  --color-brand-gray-1012: #a8a8a8;
  --color-brand-gray-1013: #7e7e7e;
  --color-brand-gray-1014: #f3f3f2;
  --color-brand-blue-50: #6dbffa;
  --color-brand-blue-100: #649ff9;
  --color-brand-blue-200: #23a0fa;
  --color-brand-blue-300: #0040bb;
  --color-brand-blue-400: #5eb4f2;
  --color-brand-black-50: #2e2e2e;
  --color-brand-black-100: #202325d9;
  --color-brand-black-150: #00000017;
  --color-brand-black-200: #2b2b2b;
  --color-brand-black-300: #3e3e3e;
  --color-brand-red-500: #e34343;
  --color-brand-red-600: #da7070;
  --color-brand-red-700: #ec0000;
  --color-brand-yellow-50: #d05257;
  --color-brand-yellow-100: #fdf4e7;
  --color-brand-facebook: #3383fb;

  /* Custom theme shadows */
  --shadow-custom: 0px 2px 8px -6px rgba(0, 0, 0, 0.08);
  --shadow-custom-2: 0px 0px 46.3px var(--color-brand-blue-50);
  --shadow-custom-3: 0px 19px 35.3px -11px var(--color-brand-blue-50);
  --shadow-custom-4: 0px 0px 25px 0px #00000040;
  --shadow-custom-5: 0px 2px 9px 0px #0000001a;
  --shadow-custom-6: 0px 2px 9px 0px #00000033;
  --shadow-custom-7: 0px 0px 25px 0px #00000026;
  --shadow-custom-8: 0px 0px 23.7px 0px #00000012;
  --shadow-location-dropdown: 0px 0px 25px 0px #00000012;
  --shadow-customHover: 0px 8px 20.8px -5px (--color-brand-blue-50);
  --shadow-customBox: 0px 4px 30px 0px #0000001a;
  --shadow-customBorder: 0px 2px 4.6px 0px #0000002e;
  --shadow-customBox2: 0px 7px 10px 0px #00000021;
  --shadow-customBox3: 0px 4px 20px 0px #00000021;
  --gradient-custom: linear-gradient(
      180deg,
      var(--color-brand-blue-50) 0%,
      var(--color-brand-600) 100%
    )
    linear-gradient(
      90deg,
      var(--color-brand-blue-50) 0%,
      var(--color-brand-blue-50) 100%
    );
  --border-line: linear-gradient(
    90deg,
    #f5f5f5 0%,
    var(--color-brand-gray-850) 40.5%,
    var(--color-brand-gray-850) 100%
  );
  --background-custom2: linear-gradient(
    90deg,
    var(--color-brand-blue-50) 0%,
    var(--color-brand-blue-50) 100%
  );
  --background-custom3: linear-gradient(
    90deg,
    rgba(35, 160, 250, 0.65) 0%,
    rgba(108, 191, 251, 0.65) 100%
  );
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-normal;
    font-family: var(--font-made-tommy-soft), system-ui, arial;
    font-weight: 400;
  }

  /* Ensure Made Tommy Soft is used everywhere with default weight 400 */
  * {
    font-family: inherit;
  }
}

.filters-modal-content button.ring-offset-background,
.signup-modal-content button.ring-offset-background {
  display: none;
}

.no-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.no-scroll::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Opera */
}

.search-filter:after {
  background-color: var(--color-brand-gray-800);
  content: "";
  z-index: 2;
  width: 2px;
  height: 44px;
  position: absolute;
  top: 8px;
  right: -24px;
}

.search-filter:before {
  content: "";
  z-index: 2;
  background-color: #000;
  width: 62%;
  height: 4px;
  position: absolute;
  bottom: -13px;
}

.secondary-button {
  background: linear-gradient(
    90deg,
    var(--color-brand-blue-50) 0%,
    var(--color-brand-blue-50) 100%
  );
  box-shadow: 0px 19px 35.3px -11px var(--color-brand-blue-50);
}
.bg-background-custom3 {
  background: linear-gradient(
    90deg,
    var(--color-brand-blue-50) 0%,
    var(--color-brand-blue-50) 100%
  );
}

.gradient-text {
  background: linear-gradient(
    95.14deg,
    #f26e20 3.3%,
    #fd1e31 20.41%,
    #904291 61.35%,
    #5155ea 106.41%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  display: inline-block; /* This might be needed for proper rendering */
  font-weight: 500;
}

.bg-background-disabled {
  background: linear-gradient(
    90deg,
    rgba(202, 202, 202, 0.65) 0%,
    rgba(135, 135, 135, 0.65) 100%
  );
}

.gradient-border-wrapper {
  padding: 2px;
  background: linear-gradient(117.09deg, #b8e1ff 5.04%, #5fb5f2 112.74%);
  transition: background 0.3s ease;
}

.gradient-border-wrapper:focus-within {
  background: linear-gradient(117.09deg, #b8f2ff 5.04%, #5f65f2 112.74%);
}

.active-tab {
  position: relative;
}

.active-tab::before {
  content: "";
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 83px;
  height: 5px;
  background-color: #000;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
  .container.inner-page-container {
    max-width: 1330px;
  }
}
