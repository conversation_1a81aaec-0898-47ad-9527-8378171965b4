"use client";

import { useState } from "react";
import { HowItWorksCard } from "@/components/Cards/how-it-works-card";
import { cn } from "@/lib/utils";
import { EnjoyIcon, PaidIcon, RequestIcon, SearchIcon, UploadIcon } from "../Icons";

interface Step {
    step: number;
    icon: React.ElementType;
    title: string;
    description: string;
}

type Tab = "renters" | "owners";

const rentersSteps: Step[] = [
    {
        step: 1,
        icon: SearchIcon,
        title: "Search",
        description: "Find the items you need quickly and easily.",
    },
    {
        step: 2,
        icon: RequestIcon,
        title: "Request",
        description: "Choose your dates, send a request, and get approved.",
    },
    {
        step: 3,
        icon: EnjoyIcon,
        title: "Enjoy",
        description: "Enjoy your rental! Just don’t forget to return it :)",
    },
];

const ownersSteps: Step[] = [
    {
        step: 1,
        icon: UploadIcon,
        title: "List rentals",
        description: "List your items quickly and easily.",
    },
    {
        step: 2,
        icon: RequestIcon,
        title: "Accept",
        description: "Review and approve rental requests on your schedule.",
    },
    {
        step: 3,
        icon: PaidIcon,
        title: "Get paid",
        description: "Earn money for sharing things you own.",
    },
];

export default function HowItWorksSection() {
    const [activeTab, setActiveTab] = useState<Tab>("renters");

    const steps = activeTab === "renters" ? rentersSteps : ownersSteps;

    return (
        <section className="w-full bg-white mt-40">
            <div className="container mx-auto text-center">
                <p className="text-brand-200 mb-2 text-3xl font-light">Renting Made Simple.</p>
                <h2 className="text-4xl md:text-7xl font-medium text-brand-600 mb-10">
                    How does it work?
                </h2>

                {/* Toggle */}
                <div className="flex justify-center gap-2 mb-32">
                    {(
                        [
                            { key: "renters", label: "for Renters" },
                            { key: "owners", label: "for Owners" },
                        ] as { key: Tab; label: string }[]
                    ).map((tab) => (
                        <button
                            key={tab.key}
                            onClick={() => setActiveTab(tab.key)}
                            className={cn(
                                "px-9 py-4 rounded-full text-xl font-medium transition-colors cursor-pointer",
                                activeTab === tab.key
                                    ? "bg-brand-1000 text-white"
                                    : "bg-brand-1100 text-brand-600 hover:bg-brand-400"
                            )}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>

                {/* Steps */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 ">
                    {steps.map(({ step, icon, title, description }) => (
                        <HowItWorksCard
                            key={step}
                            step={step}
                            icon={icon}
                            title={title}
                            description={description}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
} 