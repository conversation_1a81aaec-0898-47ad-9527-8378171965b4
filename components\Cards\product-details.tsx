"use client";

import { useState } from "react";
import Image from "next/image";
import { Star, Heart, Share, CheckSquare } from "lucide-react";
import { cn } from "@/lib/utils";

interface Owner {
    name: string;
    memberSince: string;
    avatar: string;
}

interface ProductDetailsProps {
    title: string;
    location: string;
    distance: string;
    rating: number;
    owner: Owner;
    description: string;
    features: string[];
    rentalRules: string[];
    className?: string;
}

// Rating Star Component
const RatingStar = ({ rating }: { rating: number }) => {
    if (rating === 0) {
        return <Star size={16} className="fill-gray-400 text-gray-400" />;
    } else if (rating < 4.0) {
        return (
            <div className="relative">
                <Star size={16} className="fill-gray-400 text-gray-400" />
                <div className="absolute inset-0 overflow-hidden w-1/2">
                    <Star size={16} className="fill-brand-500 text-brand-500" />
                </div>
            </div>
        );
    } else {
        return <Star size={16} className="fill-brand-500 text-brand-500" />;
    }
};

export default function ProductDetails({
    title,
    location,
    distance,
    rating,
    owner,
    description,
    rentalRules,
    className
}: ProductDetailsProps) {
    const [isSaved, setIsSaved] = useState(false);

    return (
        <div className={cn("space-y-6", className)}>
            {/* Title and Location */}
            <div>
                <h1 className="text-3xl font-medium text-brand-600 mb-2">{title}</h1>
                <div className="flex items-center gap-2 text-brand-100">
                    <span>{location}</span>
                    <span>•</span>
                    <span>{distance}</span>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                        <RatingStar rating={rating} />
                        <span className="text-base font-medium text-brand-100">
                            {rating === 0 ? "(0)" : rating.toFixed(1)}
                        </span>
                    </div>
                </div>
            </div>

            {/* Owner Info */}
            <div className="flex items-center justify-between border-t border-b border-brand-600/20 py-6">
                <div className="flex items-center gap-3">
                    <div className="relative w-12 h-12 rounded-full overflow-hidden bg-brand-gray-300">
                        <Image
                            src={owner.avatar}
                            alt={owner.name}
                            fill
                            className="object-cover"
                        />
                    </div>
                    <div>
                        <div className="flex">
                            <Image src={"/images/shield.png"} width={24} height={24} alt="Shield" className="w-[24px] h-[24px]" />
                            <h3 className="font-medium text-black text-xl ml-2">
                                {owner.name}
                            </h3>

                        </div>
                        <p className="text-sm text-brand-gray-1005">Member since {owner.memberSince}</p>
                    </div>
                </div>
                <button className="bg-brand-1000 hover:bg-brand-blue-100 text-white px-6 py-2 rounded-full font-medium transition-colors">
                    Message
                </button>
            </div>

            {/* Description */}
            <div className="border-b border-brand-600/20 pb-6">
                <h2 className="text-3xl font-medium text-black mb-4">Description</h2>
                <p className="text-black tracking-[-1%] leading-[150%] text-lg">{description}</p>
                <div className="flex items-center gap-4 pt-4 ">
                    <button
                        onClick={() => setIsSaved(!isSaved)}
                        className="flex items-center gap-2 text-black cursor-pointer hover:text-brand-600 transition-colors"
                    >
                        <Heart
                            size={20}
                            className={cn(
                                "transition-colors",
                                isSaved ? "fill-red-500 text-red-500" : "hover:text-red-500"
                            )}
                        />
                        Save
                    </button>
                    <button className="flex items-center gap-2 text-black cursor-pointer hover:text-brand-600 transition-colors">
                        <Share size={20} />
                        Share
                    </button>
                </div>
            </div>

            {/* Save and Share */}

            {/* Rental Rules */}
            <div className="border-b border-brand-600/20 pb-6">
                <h2 className="text-3xl font-medium text-black mb-6">Rental rules</h2>
                <div className="space-y-4">
                    {rentalRules.map((rule, index) => (
                        <div key={index} className="flex items-start gap-4">
                            <CheckSquare size={27} className="text-brand-600" />
                            <span className="text-brand-gray-1005 leading-relaxed">{rule}</span>
                        </div>
                    ))}
                </div>
            </div>
            <div className="border-b border-brand-600/20 pb-6">
                <h2 className="text-3xl font-medium text-black mb-6">Location</h2>
                <Image src={"/images/map.png"} alt="" width={700} height={400} />
            </div>
        </div>
    );
}
