import { useState, useRef, useEffect, useCallback } from "react"
import { Camera } from "lucide-react"

interface SelfieCameraScreenProps {
    onContinue: (capturedImage: string) => void
    onComplete?: () => void
    onBack?: () => void
    onRetake?: () => void
    capturedImage?: string | null
}

export default function SelfieCameraScreen({ onContinue, onComplete, capturedImage: externalCapturedImage }: SelfieCameraScreenProps) {
    const [capturedImage, setCapturedImage] = useState<string | null>(externalCapturedImage || null)
    const [countdown, setCountdown] = useState<number | null>(null)
    const [isCountingDown, setIsCountingDown] = useState(false)
    const [isCameraActive, setIsCameraActive] = useState(false)
    const [cameraError, setCameraError] = useState<string | null>(null)

    const videoRef = useRef<HTMLVideoElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const streamRef = useRef<MediaStream | null>(null)
    const prevCapturedImageRef = useRef<string | null | undefined>(externalCapturedImage)

    // Initialize camera with front-facing preference for selfies
    const startCamera = useCallback(async () => {
        if (isCameraActive) {
            return
        }

        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('getUserMedia not supported')
            setCameraError('Camera not supported on this device')
            return
        }

        try {
            setCameraError(null)

            // Try front camera first for selfies, fallback to any camera
            let stream;
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user', // Front camera for selfies
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                })
            } catch (frontCameraError) {
                console.log('Front camera failed, trying fallback:', frontCameraError)
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                })
            }

            if (videoRef.current && stream) {
                const videoEl = videoRef.current
                videoEl.srcObject = stream
                streamRef.current = stream
                // Attempt to play video immediately
                videoEl.play().catch(() => { })
                setIsCameraActive(true)

                // Ensure camera shows even if metadata event fails on some devices
                const handlePlaying = () => {
                    setIsCameraActive(true)
                    videoEl.removeEventListener('playing', handlePlaying)
                }
                videoEl.addEventListener('playing', handlePlaying)
            } else {
                console.error('Video ref or stream not available:', {
                    videoRef: !!videoRef.current,
                    stream: !!stream
                })
            }
        } catch (error) {
            console.error('Camera error:', error)
            setCameraError('Unable to access camera. Please ensure you have granted camera permissions and try refreshing the page.')
        }
    }, [isCameraActive])

    // Stop camera
    const stopCamera = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop())
            streamRef.current = null
        }
        setIsCameraActive(false)
    }

    // Start countdown and capture selfie
    const handleTakePhoto = () => {
        if (!isCameraActive || isCountingDown) return

        setIsCountingDown(true)
        setCountdown(3)

        const countdownInterval = setInterval(() => {
            setCountdown(prev => {
                if (prev === null) return null
                if (prev <= 1) {
                    clearInterval(countdownInterval)
                    setIsCountingDown(false)
                    capturePhoto()
                    return null
                }
                return prev - 1
            })
        }, 1000)
    }

    // Capture selfie from video stream
    const capturePhoto = () => {
        if (videoRef.current && canvasRef.current) {
            const canvas = canvasRef.current
            const video = videoRef.current
            const context = canvas.getContext('2d')

            if (context) {
                // Set canvas size to match video
                canvas.width = video.videoWidth
                canvas.height = video.videoHeight

                // Draw current video frame to canvas
                context.drawImage(video, 0, 0, canvas.width, canvas.height)

                // Convert canvas to base64 image
                const imageData = canvas.toDataURL('image/jpeg', 0.8)
                setCapturedImage(imageData)
                onContinue(imageData)

                // Stop camera immediately after capturing
                stopCamera()
                setIsCameraActive(false)
            }
        }
    }

    // Start camera when component mounts
    useEffect(() => {
        if (!capturedImage && !isCameraActive) {
            startCamera()
        }

        // Cleanup: stop camera when component unmounts
        return () => {
            stopCamera()
        }
    }, [capturedImage, isCameraActive, startCamera])

    // Update local state when external captured image changes
    useEffect(() => {
        const newCapturedImage = externalCapturedImage || null
        const prevCapturedImage = prevCapturedImageRef.current

        setCapturedImage(newCapturedImage)

        // If external image was cleared (went from having image to null), restart camera
        if (prevCapturedImage && !newCapturedImage) {
            startCamera()
        }

        // Update ref for next comparison
        prevCapturedImageRef.current = externalCapturedImage
    }, [externalCapturedImage, startCamera])

    const canTakePhoto = isCameraActive && !cameraError && !capturedImage

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-4">
                    Take a photo of yourself
                </h2>

                <p className="text-brand-black-300 text-base mb-8">
                    To complete this step, simply use your device&apos;s camera to snap a clear photo of yourself, ensuring your face is well-lit and fully visible.
                </p>

                {/* Camera Viewfinder Area */}
                <div className="flex justify-center">
                    <div className="relative w-full">
                        {capturedImage ? (
                            // Show captured image
                            <div className="relative w-full h-[300px] flex items-center justify-center overflow-hidden">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                    src={capturedImage}
                                    alt="Captured Selfie"
                                    className="w-full h-full object-cover border-white rounded-lg"
                                    style={{ borderWidth: '3px' }}
                                />
                            </div>
                        ) : (
                            <div className="w-full h-[300px] flex items-center justify-center bg-gradient-to-br from-purple-400 via-pink-300 to-purple-600 overflow-hidden relative">
                                {/* Always render video element */}
                                <video
                                    ref={videoRef}
                                    autoPlay
                                    playsInline
                                    muted
                                    className="w-full h-full object-cover"
                                    style={{ display: isCameraActive ? 'block' : 'none' }}
                                />

                                {/* Face circle guide overlay */}
                                {isCameraActive && !isCountingDown && (
                                    <div className="absolute inset-0 pointer-events-none flex items-center justify-center">
                                        {/* Face circle guide */}
                                        <div className="w-[188px] h-[226px] rounded-full border-4 border-white border-opacity-80">
                                            <div className="w-full h-full rounded-full bg-transparent" />
                                        </div>
                                    </div>
                                )}



                                {/* Overlay content when video is not active */}
                                {!isCameraActive && (
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        {cameraError ? (
                                            // Show error message
                                            <div className="text-center text-white p-4">
                                                <Camera size={48} className="mx-auto mb-4 text-gray-400" />
                                                <p className="text-sm">{cameraError}</p>
                                                <button
                                                    onClick={startCamera}
                                                    className="mt-4 px-4 py-2 bg-brand-blue-200 text-white rounded-lg text-sm"
                                                >
                                                    Try Again
                                                </button>
                                            </div>
                                        ) : (
                                            // Loading state
                                            <div className="text-center text-white">
                                                <Camera size={48} className="mx-auto mb-4 text-gray-400" />
                                                <p className="text-sm">Starting camera...</p>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Hidden canvas for capturing photos */}
                        <canvas
                            ref={canvasRef}
                            style={{ display: 'none' }}
                        />
                    </div>
                </div>
                <div className={`${capturedImage ? "border-transparent" : "border-brand-black-200"} h-[1px] border-b  my-10`} />
                {/* Take Photo Button / Countdown */}
                {!capturedImage && (
                    <div className="flex justify-center">
                        {isCountingDown && countdown !== null ? (
                            // Show countdown in place of button
                            <div className="w-full h-[60px] rounded-lg text-brand-black-200 flex items-center justify-center">
                                <div className="text-lg font-medium flex flex-col items-center justify-center">
                                    <span>
                                        Hold still...
                                    </span>
                                    <span className="text-4xl">
                                        {countdown}
                                    </span>
                                </div>
                            </div>
                        ) : (
                            // Show take photo button
                            <button
                                onClick={handleTakePhoto}
                                disabled={!canTakePhoto}
                                className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 ${canTakePhoto
                                    ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                                    : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                                    }`}
                            >
                                <Camera size={20} />
                                Take photo
                            </button>
                        )}
                    </div>
                )}

                {/* Submit Photo Button */}
                {capturedImage && (
                    <div className="flex justify-center">
                        <button
                            onClick={() => onComplete?.()}
                            className="w-full h-[60px] rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300"
                        >
                            <Camera size={20} />
                            Submit photo
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
} 