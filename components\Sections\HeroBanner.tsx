import React from "react";
import SearchBar from "../Search/SearchBar";

const HeroBanner = () => {
    return (
        <section
            className="relative flex flex-col items-center justify-center text-center min-h-screen w-full bg-cover bg-center"
            style={{
                backgroundImage: "url('/images/img-hero-bg.png')", // Placeholder, update as needed
                height: "100vh",
            }}
        >
            <div
                className="absolute inset-0 z-0"
                style={{
                    background:
                        "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.25) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15))"
                }}
            />
            <div className="relative z-10 flex flex-col items-center justify-center w-full h-full">
                <h1 className="text-white md:text-[92px] text-5xl mb-10 drop-shadow-lg leading-[110%]">
                    A Marketplace for <br /> Renting Anything!
                </h1>
                <div className="w-[1144px] mx-auto px-4 mt-8">
                    <SearchBar />
                </div>
            </div>
        </section>
    );
};

export default HeroBanner; 