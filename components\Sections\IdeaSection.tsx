"use client"
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
import { ideaCardData } from '@/data';
import { IdeaCard } from '../Cards/idea-card';
import { UploadIdea } from '../Cards/upload-idea';
import { useRef, useEffect, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import type { Swiper as SwiperType } from 'swiper';

export default function IdeaSection() {
    const prevRef = useRef<HTMLButtonElement | null>(null);
    const nextRef = useRef<HTMLButtonElement | null>(null);
    const [swiper, setSwiper] = useState<SwiperType | null>(null);

    useEffect(() => {
        if (swiper && prevRef.current && nextRef.current) {
            if (swiper.params.navigation && typeof swiper.params.navigation !== 'boolean') {
                swiper.params.navigation.prevEl = prevRef.current;
                swiper.params.navigation.nextEl = nextRef.current;
                swiper.navigation.init();
                swiper.navigation.update();
            }
        }
    }, [swiper]);

    return (
        <div className="mt-40 container mx-auto">
            <div className="text-center mb-24">
                <p className="text-brand-200 mb-2 text-3xl font-light">Need Ideas?</p>
                <h2 className="text-4xl md:text-7xl font-medium text-brand-600 ">
                    What could I list on <br /> YouHook?
                </h2>
            </div>

            <div className="relative w-full mb-10">
                <Swiper
                    slidesPerView={2}
                    spaceBetween={30}
                    centeredSlides={true}
                    modules={[Navigation]}
                    navigation={{
                        prevEl: prevRef.current,
                        nextEl: nextRef.current,
                    }}
                    breakpoints={{
                        640: {
                            slidesPerView: 2,
                            spaceBetween: 20,
                        },
                        768: {
                            slidesPerView: 3,
                            spaceBetween: 30,
                        },
                        1024: {
                            slidesPerView: 4,
                            spaceBetween: 30,
                        },
                    }}
                    onSwiper={setSwiper}
                    className="mySwiper"
                >
                    {ideaCardData.map((item, idx) => (
                        <SwiperSlide className='py-4' key={idx}>
                            <IdeaCard {...item} />
                        </SwiperSlide>
                    ))}
                    <SwiperSlide>
                        <UploadIdea onClick={() => console.log("Upload item clicked!")} />
                    </SwiperSlide>
                </Swiper>

                {/* Custom navigation buttons */}
                <button
                    ref={prevRef}
                    className="absolute cursor-pointer -left-6 top-1/2 -translate-y-1/2 z-10 flex items-center justify-center w-10 h-10 rounded-full bg-brand-400 text-brand-600 hover:bg-brand-1000 hover:text-white "
                >
                    <ChevronLeft size={20} />
                </button>
                <button
                    ref={nextRef}
                    className="absolute cursor-pointer -right-6 top-1/2 -translate-y-1/2 z-10 flex items-center justify-center w-10 h-10 rounded-full bg-brand-400 text-brand-600 hover:bg-brand-1000 hover:text-white  "
                >
                    <ChevronRight size={20} />
                </button>
            </div>
            <p className="text-center text-xl text-brand-200 mt-8">Here are some popular and creative options to help you get started!</p>
        </div>

    );
}