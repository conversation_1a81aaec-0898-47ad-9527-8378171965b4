import { useState, useCallback } from "react"
import dynamic from "next/dynamic"

// react-easy-crop doesn't ship TypeScript types by default.
// Cast to any to satisfy the compiler.
/* eslint-disable @typescript-eslint/no-explicit-any */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const Cropper = dynamic<any>(() => import("react-easy-crop").then((mod) => mod.default), { ssr: false })

interface ImageCropperProps {
    imageSrc: string
    onCropComplete: (croppedImage: string) => void
}

const createImage = (url: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        const img = new Image()
        img.addEventListener("load", () => resolve(img))
        img.addEventListener("error", (error) => reject(error))
        img.setAttribute("crossOrigin", "anonymous") // to avoid CORS issues
        img.src = url
    })
}

async function getCroppedImg(
    imageSrc: string,
    pixelCrop: { width: number; height: number; x: number; y: number },
): Promise<string> {
    const image = await createImage(imageSrc)
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (!ctx) throw new Error("Canvas not supported")

    canvas.width = pixelCrop.width
    canvas.height = pixelCrop.height

    ctx.drawImage(
        image,
        pixelCrop.x,
        pixelCrop.y,
        pixelCrop.width,
        pixelCrop.height,
        0,
        0,
        pixelCrop.width,
        pixelCrop.height
    )

    return canvas.toDataURL("image/jpeg", 0.9)
}

export default function ImageCropper({ imageSrc, onCropComplete }: ImageCropperProps) {
    const [crop, setCrop] = useState({ x: 0, y: 0 })
    const [zoom] = useState(1)
    const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
        width: number; height: number; x: number; y: number
    } | null>(null)

    const onCropCompleteInternal = useCallback((_: any, areaPixels: any) => {
        setCroppedAreaPixels(areaPixels)
    }, [])

    const handleDone = useCallback(async () => {
        if (!croppedAreaPixels) return
        try {
            const croppedImg = await getCroppedImg(imageSrc, croppedAreaPixels)
            onCropComplete(croppedImg)
        } catch (e) {
            console.error(e)
        }
    }, [croppedAreaPixels, imageSrc, onCropComplete])

    return (
        <div className="px-8 pb-8 w-full">
            <div className="text-center pt-6">
                {/* No zoom/rotate controls as per latest requirements */}

                {/* Cropper */}
                <div className="mb-10 flex justify-center">
                    <div className="relative w-[400px] h-[400px] bg-gray-100">
                        <Cropper
                            image={imageSrc}
                            crop={crop}
                            zoom={zoom}
                            aspect={1}
                            cropSize={{ width: 250, height: 250 }}
                            onCropChange={setCrop}
                            onCropComplete={onCropCompleteInternal}
                            showGrid={true}
                            cropShape="rect"
                            cropAreaStyle={{ border: "4px solid #3b82f6" }}
                            restrictPosition={false}
                            zoomWithScroll={false}
                        />
                    </div>
                </div>

                {/* Instruction text removed as per latest requirements */}

                {/* Buttons */}
                <div className="space-y-4">
                    <button
                        onClick={handleDone}
                        className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium flex items-center justify-center gap-2 cursor-pointer"
                    >
                        Done
                    </button>
                </div>
            </div>
        </div>
    )
} 