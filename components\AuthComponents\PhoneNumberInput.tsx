'use client'

import { InputMask } from '@react-input/mask'
import { useState } from 'react'

export default function PhoneNumberInput({
    countryCode,
    phone,
    onPhoneChange,
}: {
    countryCode: string
    phone: string
    onPhoneChange: (val: string) => void
}) {
    const [focused, setFocused] = useState(false)
    const isFloating = focused || phone.length > 0

    return (
        <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2  transition-all duration-300 ${isFloating ? 'border-brand-black-200 border-2 rounded-lg' : 'border-b-transparent border-t border-brand-gray-960 '}`}>
            {/* Floating Label */}
            <label
                className={`
                    absolute left-3 bg-white block w-1/2 text-brand-gray-970 transition-all pointer-events-none 
                    ${isFloating ? 'top-0 text-xs ' : 'top-1/2 -translate-y-1/2 text-xl'}
                `}
            >
                Phone Number
            </label>

            {/* Static Country Code */}
            <span className="text-xl text-black mr-2 select-none whitespace-nowrap">{countryCode}</span>

            {/* Masked Input */}
            <InputMask
                mask="(*************-9999"
                replacement={{ 9: /\d/ }}
                value={phone}
                onChange={(e) => onPhoneChange(e.target.value)}
                onFocus={() => setFocused(true)}
                onBlur={() => setFocused(false)}
                placeholder="(XXX) XXX-XXX"
                className="border-none w-full h-full outline-none text-xl"
            />
        </div>
    )
}
