import type { Config } from "tailwindcss";

export default {
  // darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        "made-tommy-soft": [
          "var(--font-made-tommy-soft)",
          "system-ui",
          "arial",
        ],
        sans: ["var(--font-made-tommy-soft)", "system-ui", "arial"],
      },
      boxShadow: {
        custom: "var(--shadow-custom)",
        customHover: "var(--shadow-customHover)",
        customBox: "var(--shadow-customBox)",
        customBorder: "var(--shadow-customBorder)",
        customBlue: "var(--shadow-customBlue)",
        customBorder2: "var(--shadow-custom-2)",
        shadowCustom3: "var(--shadow-custom-3)",
        shadowCustomBox2: "var(--shadow-customBox2)",
        shadowCustomBox3: "var(--shadow-customBox3)",
        "custom-black": "0px 4px 12px 0px #00000040",
        shadowCustom4: "var(--shadow-custom-4)",
        shadowCustom5: "var(--shadow-custom-5)",
        shadowCustom6: "var(--shadow-custom-6)",
        shadowCustom7: "var(--shadow-custom-7)",
        shadowCustom8: "var(--shadow-custom-8)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",

        brand: {
          100: "var(--color-brand-100)",
          200: "var(--color-brand-200)",
          300: "var(--color-brand-300)",
          400: "var(--color-brand-400)",
          500: "var(--color-brand-500)",
          600: "var(--color-brand-600)",
          700: "var(--color-brand-700)",
          800: "var(--color-brand-800)",
          900: "var(--color-brand-900)",
          1000: "var(--color-brand-1000)",
          1100: "var(--color-brand-1100)",
          1200: "var(--color-brand-1200)",
          1300: "var(--color-brand-1300)",
          1400: "var(--color-brand-1400)",
          1500: "var(--color-brand-1500)",
          1600: "var(--color-brand-1600)",
          1700: "var(--color-brand-1700)",
          green: "var(--color-brand-green)",
          gray50: "var(--color-brand-gray-50)",
          gray100: "var(--color-brand-gray-100)",
          gray200: "var(--color-brand-gray-200)",
          gray300: "var(--color-brand-gray-300)",
          gray350: "var(--color-brand-gray-350)",
          gray400: "var(--color-brand-gray-400)",
          gray450: "var(--color-brand-gray-450)",
          blue50: "var(--color-brand-blue-50)",
          blue100: "var(--color-brand-blue-100)",
          blue200: "var(--color-brand-blue-200)",
          blue300: "var(--color-brand-blue-300)",
          blue400: "var(--color-brand-blue-400)",
          black50: "var(--color-brand-black-50)",
          black150: "var(--color-brand-black-150)",
          black200: "var(--color-brand-black-200)",
          black300: "var(--color-brand-black-300)",
          gray500: "var(--color-brand-gray-500)",
          gray600: "var(--color-brand-gray-600)",
          gray650: "var(--color-brand-gray-650)",
          gray700: "var(--color-brand-gray-700)",
          gray750: "var(--color-brand-gray-750)",
          gray800: "var(--color-brand-gray-800)",
          gray900: "var(--color-brand-gray-900)",
          gray960: "var(--color-brand-gray-960)",
          gray970: "var(--color-brand-gray-970)",
          gray950: "var(--color-brand-gray-950)",
          gray1000: "var(--color-brand-gray-1000)",
          gray1001: "var(--color-brand-gray-1001)",
          gray1002: "var(--color-brand-gray-1002)",
          gray1003: "var(--color-brand-gray-1003)",
          gray1004: "var(--color-brand-gray-1004)",
          gray1005: "var(--color-brand-gray-1005)",
          gray1006: "var(--color-brand-gray-1006)",
          gray1007: "var(--color-brand-gray-1007)",
          gray1008: "var(--color-brand-gray-1008)",
          gray1009: "var(--color-brand-gray-1009)",
          gray1010: "var(--color-brand-gray-1010)",
          gray1011: "var(--color-brand-gray-1011)",
          gray1012: "var(--color-brand-gray-1012)",
          gray1013: "var(--color-brand-gray-1013)",
          gray1014: "var(--color-brand-gray-1014)",
          gray660: "var(--color-brand-gray-660)",
          red500: "var(--color-brand-red-500)",
          red600: "var(--color-brand-red-600)",
          red700: "var(--color-brand-red-700)",
          facebook: "var(--color-brand-facebook)",
          gray995: "var(--color-brand-gray-995)",
          yellow100: "var(--color-brand-yellow-100)",
          yellow50: "var(--color-brand-yellow-50)",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      backgroundImage: {
        "gradient-custom": "var(--gradient-custom)",
        "border-line": "var(--border-line)",
        "background-custom2": "var(--background-custom2)",
        "background-custom3": "var(--background-custom3)",
      },
      backgroundColor: {
        "custom-bg": "#20232566",
      },
      textColor: {
        "custom-text": "#20232566",
      },
      borderColor: {
        "custom-border": "#20232566",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [],
} satisfies Config;
