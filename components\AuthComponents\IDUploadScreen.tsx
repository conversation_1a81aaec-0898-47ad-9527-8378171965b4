import { useState } from "react"
import { Lock } from "lucide-react"
import Image from "next/image"
import { ReUploadButton } from "@/components/Buttons"

interface IDUploadScreenProps {
    idType: "drivers-license" | "passport" | "identity-card"
    onContinue: () => void
    onBack?: () => void
}

export default function IDUploadScreen({ idType, onContinue }: IDUploadScreenProps) {
    const [frontImage, setFrontImage] = useState<string | null>(null)
    const [backImage, setBackImage] = useState<string | null>(null)

    const getIDTypeDisplay = () => {
        switch (idType) {
            case "drivers-license":
                return "driver's license"
            case "passport":
                return "passport"
            case "identity-card":
                return "identity card"
            default:
                return "ID"
        }
    }

    const createFileInput = (type: "front" | "back") => {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = 'image/*'
        input.style.display = 'none'

        input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0]
            if (file) {
                const reader = new FileReader()
                reader.onload = (e) => {
                    const result = e.target?.result as string
                    if (type === "front") {
                        setFrontImage(result)
                    } else {
                        setBackImage(result)
                    }
                }
                reader.readAsDataURL(file)
            }
            // Clean up
            if (input.parentNode) {
                input.parentNode.removeChild(input)
            }
        }

        document.body.appendChild(input)
        input.click()
    }

    const handleUploadFront = () => {
        createFileInput("front")
    }

    const handleUploadBack = () => {
        createFileInput("back")
    }

    const handleReUploadFront = () => {
        setFrontImage(null)
        createFileInput("front")
    }

    const handleReUploadBack = () => {
        setBackImage(null)
        createFileInput("back")
    }

    const canContinue = frontImage && backImage

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-6">
                    Upload images of your {getIDTypeDisplay()}
                </h2>

                <p className="text-brand-black-300 text-lg mb-8">
                    Make sure photos are not blurry and the front of your  {getIDTypeDisplay()} clearly shows your face.
                </p>

                <div className="grid grid-cols-2 gap-4 mb-12">
                    {/* Front Upload */}
                    <div >
                        {frontImage ? (
                            <div className="space-y-3">
                                <div className="w-full h-[175px] border py-3 border-brand-gray-1004 rounded-lg overflow-hidden">
                                    {/* eslint-disable-next-line @next/next/no-img-element */}
                                    <img
                                        src={frontImage}
                                        alt="Front ID"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <ReUploadButton
                                    onClick={handleReUploadFront}
                                    text="Re-upload front"
                                />
                            </div>
                        ) : (
                            <div
                                onClick={handleUploadFront}
                                className="w-full h-[175px] border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
                            >
                                <Image src="/images/identity_1.png" alt="Front ID" width={50} height={50} />
                                <span className="text-brand-black-300 font-medium text-xl mb-1">Upload front</span>
                                <span className="text-brand-gray-960 text-sm">JPEG or PNG</span>
                            </div>
                        )}
                    </div>

                    {/* Back Upload */}
                    <div >
                        {backImage ? (
                            <div className="space-y-3">
                                <div className="w-full h-[175px] border py-3 border-brand-gray-1004 rounded-lg overflow-hidden">
                                    {/* eslint-disable-next-line @next/next/no-img-element */}
                                    <img
                                        src={backImage}
                                        alt="Back ID"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <ReUploadButton
                                    onClick={handleReUploadBack}
                                    text="Re-upload back"
                                />
                            </div>
                        ) : (
                            <div
                                onClick={handleUploadBack}
                                className="w-full h-[175px] border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors "
                            >
                                <div className="text-brand-gray-1004 mb-2">
                                    <Image src="/images/credit-card_1.png" alt="Back ID" width={45} height={45} />
                                </div>
                                <span className="text-brand-black-300 font-medium text-xl mb-1">Upload back</span>
                                <span className="text-brand-gray-960 text-sm">JPEG or PNG</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Privacy Notice */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8 text-left">
                    <h4 className="font-medium text-brand-black-300 mb-2 flex items-center gap-2">
                        <Lock size={16} />
                        Your Privacy
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                        We take your privacy seriously. Your ID and data will be kept safe, secure
                        and will never be shared with anyone. <span className="underline cursor-pointer">Learn more.</span>
                    </p>
                </div>

                {/* Continue Button */}
                <div className="flex justify-center">
                    <button
                        onClick={onContinue}
                        disabled={!canContinue}
                        className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 ${canContinue
                            ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                            : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        <Lock size={16} />
                        Continue
                    </button>
                </div>
            </div>
        </div>
    )
} 