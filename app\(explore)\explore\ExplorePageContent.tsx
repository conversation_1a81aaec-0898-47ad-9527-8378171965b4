'use client'
import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import CategorySlider from "@/components/CategorySlider";
import { ProductionIcon, SportsIcon, ToolsIcon, AttireIcon, AdventureIcon, SearchIcon2, SpacesIcon } from "@/components/Icons";
import Filters from "@/components/Search/Filters";
import FiltersModal from "@/components/Search/FiltersModal";
import { primaryCardData } from "@/data";
import { PrimaryCard } from "@/components/Cards";

export default function ExplorePageContent() {
    const router = useRouter();
    const [loadingState, setLoadingState] = useState < 'first' | 'second' | 'data' > ('first');
    const searchParams = useSearchParams();
    const paramsKey = searchParams.toString();

    // Filter state
    const [activeFilter, setActiveFilter] = useState < string > ('All rentals');

    // Available filters
    const filterOptions = [
        { id: 'all', text: 'All rentals', Icon: SearchIcon2 },
        { id: 'spaces', text: 'Spaces', Icon: SpacesIcon },
        { id: 'adventure', text: 'Adventure', Icon: AdventureIcon },
        { id: 'attire', text: 'Attire', Icon: AttireIcon },
        { id: 'production', text: 'Production', Icon: ProductionIcon },
        { id: 'tools', text: 'Tools', Icon: ToolsIcon },
        { id: 'sports', text: 'Sports', Icon: SportsIcon },
        { id: 'electronics', text: 'Electronics', Icon: SportsIcon },
        { id: 'furniture', text: 'Furniture', Icon: SportsIcon },
        { id: 'animals', text: 'Animals', Icon: SportsIcon },
        { id: 'kids', text: 'Kids & babies', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
        { id: 'other', text: 'Other', Icon: SportsIcon },
    ];

    // Sync active filter with URL params
    useEffect(() => {
        const categoryParam = searchParams.get('category') || 'all';
        const selectedFilter = filterOptions.find(f => f.id === categoryParam);
        setActiveFilter(selectedFilter?.text || 'All rentals');
    }, [searchParams]);

    // Handle filter click
    const handleFilterClick = (filter: typeof filterOptions[0]) => {
        setActiveFilter(filter.text);

        // Get existing search params
        const currentQuery = searchParams.get('query') || '';
        const currentLocation = searchParams.get('location') || '';

        // Build new URL with filter
        const params = new URLSearchParams();
        if (currentQuery) params.set('query', currentQuery);
        if (currentLocation) params.set('location', currentLocation);
        if (filter.id !== 'all') params.set('category', filter.id);

        router.push(`/explore?${params.toString()}`);
    };

    // Loading skeletons – reset whenever search params change
    useEffect(() => {
        const firstTimer = setTimeout(() => {
            setLoadingState('second');
        }, 1000);

        const secondTimer = setTimeout(() => {
            setLoadingState('data');
        }, 2000);

        return () => {
            clearTimeout(firstTimer);
            clearTimeout(secondTimer);
        };
    }, [paramsKey]);

    const FirstSkeleton = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
            {Array.from({ length: 8 }).map((_, idx) => (
                <div key={idx} className="animate-pulse">
                    <div className="bg-brand-gray-1008 h-[315px] rounded-lg mb-4 border border-brand-gray-900"></div>
                    <div className="flex justify-between">
                        <div className="h-[30px] bg-brand-gray-1008 rounded mb-2 w-[100px]"></div>
                        <div className="h-[30px] bg-brand-gray-1008 rounded w-[60px]"></div>
                    </div>
                    <div className="h-[30px] bg-brand-gray-1008 rounded w-[150px]"></div>
                </div>
            ))}
        </div>
    );

    const SecondSkeleton = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
            {Array.from({ length: 8 }).map((_, idx) => (
                <div key={idx} className="animate-pulse">
                    <div className="bg-brand-gray-1007 h-[315px] rounded-lg mb-4 border border-brand-gray-900"></div>
                    <div className="flex justify-between">
                        <div className="h-[30px] bg-brand-gray-1007 rounded mb-2 w-[100px]"></div>
                        <div className="h-[30px] bg-brand-gray-1007 rounded w-[60px]"></div>
                    </div>
                    <div className="h-[30px] bg-brand-gray-1007 rounded w-[150px]"></div>
                </div>
            ))}
        </div>
    );

    return (
        <>
            <div className="flex py-3 gap-x-14 items-center px-10 border-b border-brand-gray-850 justify-center">
                <div className="w-[8%]">
                    <Filters
                        Icon={SearchIcon2}
                        text="All rentals"
                        isActive={activeFilter === 'All rentals'}
                        isSearchFilter={true}
                        onClick={() => handleFilterClick(filterOptions[0])}
                    />
                </div>
                <div className="flex-1 w-[80%]">
                    <CategorySlider>
                        {filterOptions.slice(1).map((filter) => (
                            <Filters
                                key={filter.id}
                                Icon={filter.Icon}
                                text={filter.text}
                                isActive={activeFilter === filter.text}
                                onClick={() => handleFilterClick(filter)}
                            />
                        ))}
                    </CategorySlider>
                </div>
                <div className="text-center w-[8%]">
                    <FiltersModal />
                </div>
            </div>

            <div className="px-6 pt-14 pb-20">
                {loadingState === 'first' && <FirstSkeleton />}
                {loadingState === 'second' && <SecondSkeleton />}
                {loadingState === 'data' && (
                    <>
                        <div className="text-2xl text-brand-600 mb-5">Results</div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
                            {primaryCardData.map((item, idx) => (
                                <PrimaryCard key={idx} {...item} />
                            ))}
                        </div>
                    </>
                )}
            </div>
        </>
    );
}