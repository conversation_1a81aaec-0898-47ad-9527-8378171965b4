"use client"

import { useState, type ChangeEvent } from "react"

interface PriceRangeSliderProps {
    min?: number
    max?: number
    initialMinValue?: number
    initialMaxValue?: number
    step?: number
    onChange?: (values: { min: number; max: number }) => void
}

export default function PriceRangeSlider({
    min = 1,
    max = 1000,
    initialMinValue = 1,
    initialMaxValue = 1000,
    step = 1,
    onChange,
}: PriceRangeSliderProps) {
    const [minVal, setMinVal] = useState<number>(initialMinValue)
    const [maxVal, setMaxVal] = useState<number>(initialMaxValue)

    const clamp = (val: number, minLimit: number, maxLimit: number) => Math.min(Math.max(val, minLimit), maxLimit)

    const handleMinChange = (e: ChangeEvent<HTMLInputElement>) => {
        const newVal = clamp(Number(e.target.value), min, maxVal - step)
        setMinVal(newVal)
        onChange?.({ min: newVal, max: maxVal })
    }

    const handleMaxChange = (e: ChangeEvent<HTMLInputElement>) => {
        const newVal = clamp(Number(e.target.value), minVal + step, max)
        setMaxVal(newVal)
        onChange?.({ min: minVal, max: newVal })
    }

    const minPerc = ((minVal - min) / (max - min)) * 100
    const maxPerc = ((maxVal - min) / (max - min)) * 100

    return (
        <div className="w-full">
            <div className="relative h-[3px] bg-brand-gray-300 rounded-full">
                {/* active range segment */}
                <div
                    className="absolute top-0 h-full bg-black rounded-full"
                    style={{ left: `${minPerc}%`, width: `${maxPerc - minPerc}%` }}
                />

                {/* min range input */}
                <input
                    type="range"
                    min={min}
                    max={max}
                    step={step}
                    value={minVal}
                    onChange={handleMinChange}
                    className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer z-20"
                />

                {/* max range input */}
                <input
                    type="range"
                    min={min}
                    max={max}
                    step={step}
                    value={maxVal}
                    onChange={handleMaxChange}
                    className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer z-10"
                />

                {/* Labels */}
                <span
                    className="absolute -top-10 text-black font-regular select-none pointer-events-none"
                    style={{ left: `${minPerc}%`, transform: "translateX(-50%)" }}
                >
                    {`$${minVal}`}
                </span>
                <span
                    className="absolute -top-10 -right-20 text-black font-regular select-none pointer-events-none"
                    style={{ left: `${maxPerc}%`, transform: "translateX(-50%)" }}
                >
                    {`$${maxVal === max ? maxVal + "+" : maxVal}`}
                </span>

                {/* Thumbs */}
                <div
                    className="absolute w-[26px] h-[26px] bg-white rounded-full border border-brand-100 -translate-x-1/2 -translate-y-1/2 top-1/2 pointer-events-none"
                    style={{ left: `${minPerc}%` }}
                />
                <div
                    className="absolute w-[26px] h-[26px] bg-white rounded-full border border-brand-100 -translate-x-1/2 -translate-y-1/2 top-1/2 pointer-events-none"
                    style={{ left: `${maxPerc}%` }}
                />
            </div>
        </div>
    )
} 