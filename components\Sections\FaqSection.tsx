"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { CalendarIcon } from "../Icons";
import SecondaryButton from "../Buttons/secondary-button";

interface FAQ {
    question: string;
    answer: string;
}

const faqs: FAQ[] = [
    {
        question: "Is it free to create a listing on YouHook?",
        answer:
            "Yes! Creating a listing is completely free. You only pay a service fee once your item is rented.",
    },
    {
        question: "How quickly will I get my first rental?",
        answer:
            "Many owners receive their first rental within days of listing, but timing can vary based on demand in your area and seasonality.",
    },
    {
        question: "Are there any fees on YouHook?",
        answer:
            "There are currently no listing fees. A small service fee is applied per successful rental to keep the platform running.",
    },
    {
        question: "Can I create a listing if I am under 18?",
        answer:
            "You must be 18 years or older to create a listing and enter into rental agreements on YouHook.",
    },
    {
        question:
            "I am not always available to rent my stuff, can I still create listings?",
        answer:
            "Absolutely. You control your availability calendar and can approve or decline requests based on your schedule.",
    },
    {
        question: "Does YouHook have insurance on all rentals?",
        answer:
            "Yes. Every rental is covered by our comprehensive insurance policy for extra peace of mind.",
    },
    {
        question: "Can I cancel a rental after it's booked?",
        answer:
            "Yes, but cancellation policies apply. We recommend cancelling only when absolutely necessary to maintain a great renter experience.",
    },
    {
        question: "Still have questions?",
        answer: "Reach out to our support team <NAME_EMAIL> and we'll be happy to help!",
    },
];

export default function FaqSection() {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const toggle = (idx: number) => {
        setOpenIndex((prev) => (prev === idx ? null : idx));
    };

    return (
        <section className="w-full bg-white mt-40">
            <div className="container mx-auto">
                {/* Eyebrow */}
                <div className="text-center">
                    <p className="text-brand-200 mb-4 text-xl">Still thinking?</p>

                    {/* Heading */}
                    <h2 className="text-brand-600 text-4xl md:text-7xl font-semibold mb-16">
                        Frequently asked questions
                    </h2>

                </div>

                {/* FAQ list */}
                <div className="mx-auto w-full text-left mb-10">
                    {faqs.map((item, idx) => {
                        const isOpen = idx === openIndex;
                        return (
                            <div key={idx}>
                                <button
                                    onClick={() => toggle(idx)}
                                    className={`w-full flex items-center justify-between gap-4 py-4 px-6 text-lg md:text-2xl font-normal text-brand-1600  bg-brand-1300  transition-colors rounded-md cursor-pointer border-b border-brand-1500 text-left`}
                                >
                                    <span>{item.question}</span>
                                    <ChevronDown
                                        size={24}
                                        className={`transition-transform duration-300 ${isOpen ? "rotate-180 text-brand-1600" : "rotate-0"} text-brand-1500`}
                                    />
                                </button>
                                {isOpen && (
                                    <div className="px-6 pb-6 pt-2 text-brand-200 text-base md:text-lg bg-brand-1400">
                                        {item.answer}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>

                {/* CTA */}
                <div className="flex flex-col items-center gap-4">
                    <SecondaryButton btnText="Join YouHook" href="#" />
                    <p className="flex items-center gap-2 text-brand-200 text-lg">
                        <CalendarIcon />
                        <span className="font-medium text-brand-600">Access 5+ </span> different rental methods
                    </p>
                </div>
            </div>
        </section>
    );
} 