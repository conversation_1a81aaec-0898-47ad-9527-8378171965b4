"use client";

import { ButtonHTMLAttributes, AnchorHTMLAttributes } from "react";

// Base props that both button and link versions share
interface BaseProps {
    btnText: string;
    className?: string;
    disabled?: boolean;
}

// Button-specific props
interface ButtonProps extends BaseProps, Omit<ButtonHTMLAttributes<HTMLButtonElement>, keyof BaseProps> {
    href?: never;
}

// Link-specific props  
interface LinkProps extends BaseProps, Omit<AnchorHTMLAttributes<HTMLAnchorElement>, keyof BaseProps> {
    href: string;
    target?: string;
    rel?: string;
}

type PrimaryButtonProps = ButtonProps | LinkProps;

export function PrimaryButton({
    btnText,
    className = "",
    disabled = false,
    ...props
}: PrimaryButtonProps) {
    const baseStyles = `bg-black text-white px-10 py-4 text-xl font-medium rounded-full ${className}`;

    // Render as link if href is provided
    if ("href" in props && props.href) {
        return (
            <a
                className={baseStyles}
                {...(props as AnchorHTMLAttributes<HTMLAnchorElement>)}
            >
                {btnText}
            </a>
        );
    }

    // Render as button if no href
    return (
        <button
            className={baseStyles}
            disabled={disabled}
            {...(props as ButtonHTMLAttributes<HTMLButtonElement>)}
        >
            {btnText}
        </button>
    );
} 