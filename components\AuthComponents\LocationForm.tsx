import { useState, useEffect, useRef } from "react"
import { Loader } from "@googlemaps/js-api-loader"
import { LocationPinIcon, SearchSmallIcon } from "../Icons"

/// <reference types="google.maps" />

interface LocationFormProps {
    onSubmit: (location: string) => void
    onBack?: () => void
}

// Using Google Maps AutocompletePrediction type directly
type PlacePrediction = google.maps.places.AutocompletePrediction;

export default function LocationForm({ onSubmit }: LocationFormProps) {
    const [selectedLocation, setSelectedLocation] = useState("")
    const [zipcode, setZipcode] = useState("")
    const [, setCurrentLocation] = useState("")
    const [suggestions, setSuggestions] = useState<PlacePrediction[]>([])
    const [showSuggestions, setShowSuggestions] = useState(false)
    const [isLoadingLocation, setIsLoadingLocation] = useState(true)
    const [isSearching, setIsSearching] = useState(false)
    const [placesService, setPlacesService] = useState<google.maps.places.AutocompleteService | null>(null)
    const [geocoder, setGeocoder] = useState<google.maps.Geocoder | null>(null)
    const [isLocationSelected, setIsLocationSelected] = useState(false)
    const searchTimeout = useRef<NodeJS.Timeout | null>(null)

    // Initialize Google Maps API
    useEffect(() => {
        const initializeGoogleMaps = async () => {
            try {
                const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
                console.log("API Key found:", apiKey ? "Yes" : "No")

                if (!apiKey) {
                    console.error("❌ Google Maps API key not found in environment variables")
                    console.error("Please add NEXT_PUBLIC_GOOGLE_MAPS_API_KEY to your .env.local file")
                    return
                }

                if (apiKey === "your_google_maps_api_key_here") {
                    console.error("❌ Please replace 'your_google_maps_api_key_here' with your actual Google Maps API key")
                    return
                }

                console.log("🔄 Loading Google Maps API...")

                const loader = new Loader({
                    apiKey: apiKey,
                    version: "weekly",
                    libraries: ["places"]
                })

                const google = await loader.load()
                console.log("✅ Google Maps API loaded successfully")

                const autocompleteService = new google.maps.places.AutocompleteService()
                const geocoderService = new google.maps.Geocoder()

                setPlacesService(autocompleteService)
                setGeocoder(geocoderService)
                console.log("✅ Google Maps services initialized")

            } catch (error) {
                console.error("❌ Error loading Google Maps:", error)
            }
        }

        initializeGoogleMaps()
    }, [])

    // Get user's current location and set as default selected location
    useEffect(() => {
        const getCurrentLocation = async () => {
            if (!navigator.geolocation) {
                console.log("Geolocation not supported")
                setIsLoadingLocation(false)
                return
            }

            try {
                const position = await new Promise<GeolocationPosition>((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        timeout: 10000,
                        maximumAge: 300000 // 5 minutes
                    })
                })

                if (geocoder) {
                    const { latitude, longitude } = position.coords

                    geocoder.geocode(
                        { location: { lat: latitude, lng: longitude } },
                        (results, status) => {
                            setIsLoadingLocation(false)
                            if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
                                const address = results[0].formatted_address
                                setCurrentLocation(address)
                                // Auto-select current location as default
                                setSelectedLocation(address)
                                setIsLocationSelected(true)
                            }
                        }
                    )
                } else {
                    setIsLoadingLocation(false)
                }
            } catch (error) {
                console.log("Location access denied or failed:", error)
                setIsLoadingLocation(false)
            }
        }

        if (geocoder) {
            getCurrentLocation()
        }
    }, [geocoder])

    // Get location suggestions using Google Places API
    const getLocationSuggestions = async (query: string) => {
        console.log("🔍 Searching for:", query)

        if (!placesService) {
            console.error("❌ Places service not initialized")
            return
        }

        if (query.length < 3) {
            console.log("⚠️ Query too short, clearing suggestions")
            setSuggestions([])
            setShowSuggestions(false)
            return
        }

        setIsSearching(true)
        console.log("🔄 Fetching place predictions...", "isSearching set to true")

        try {
            const request: google.maps.places.AutocompletionRequest = {
                input: query,
                types: ['geocode'], // Includes postal codes and addresses
                componentRestrictions: { country: 'us' }, // Restrict to US, change as needed
            }

            placesService.getPlacePredictions(request, (predictions: google.maps.places.AutocompletePrediction[] | null, status: google.maps.places.PlacesServiceStatus) => {
                setIsSearching(false)
                console.log("✅ Search completed, isSearching set to false")
                console.log("📊 API Response Status:", status)
                console.log("📍 Predictions received:", predictions?.length || 0)

                if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
                    setSuggestions(predictions)
                    setShowSuggestions(true)
                    console.log("✅ Suggestions displayed")
                } else {
                    setSuggestions([])
                    setShowSuggestions(false)
                    console.warn("⚠️ No predictions or API error:", status)
                }
            })
        } catch (error) {
            console.error("❌ Error fetching place predictions:", error)
            setIsSearching(false)
            setSuggestions([])
            setShowSuggestions(false)
        }
    }

    // Handle zipcode input changes (numbers only)
    const handleZipcodeChange = (value: string) => {
        // Only allow numbers
        const numericValue = value.replace(/\D/g, '')
        setZipcode(numericValue)
        setSelectedLocation("")
        setIsLocationSelected(false)

        // Clear previous timeout
        if (searchTimeout.current) {
            clearTimeout(searchTimeout.current)
        }

        // Set new timeout for debounced search
        if (numericValue.length >= 3) {
            searchTimeout.current = setTimeout(() => {
                getLocationSuggestions(numericValue)
            }, 300)
        } else {
            setSuggestions([])
            setShowSuggestions(false)
            setIsSearching(false) // Reset searching state when input is too short
        }
    }

    const handleSuggestionClick = async (suggestion: PlacePrediction) => {
        setSelectedLocation(suggestion.description)
        setZipcode("")
        setSuggestions([])
        setShowSuggestions(false)
        setIsLocationSelected(true)

        // Optional: Get more details about the selected place
        if (geocoder) {
            try {
                const result = await new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
                    geocoder.geocode({ placeId: suggestion.place_id }, (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
                        if (status === google.maps.GeocoderStatus.OK && results) {
                            resolve(results)
                        } else {
                            reject(status)
                        }
                    })
                })

                // You can store additional details like coordinates, formatted address, etc.
                console.log("Place details:", result[0])
            } catch (error) {
                console.error("Error getting place details:", error)
            }
        }
    }



    const handleConfirm = () => {
        if (selectedLocation.trim()) {
            onSubmit(selectedLocation)
        }
    }

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (searchTimeout.current) {
                clearTimeout(searchTimeout.current)
            }
        }
    }, [])

    const isButtonEnabled = isLocationSelected && selectedLocation.trim().length > 0

    return (
        <div className="px-8 pb-8">
            <div className="text-center mb-8">
                <h3 className="text-brand-black-300 text-2xl font-medium mb-6">Confirm your location</h3>

                <div className="relative">
                    <div className="w-full h-[77px] px-8 text-xl border border-brand-gray-960 rounded-full bg-white focus:outline-none focus:ring-2 focus:ring-brand-blue-200 focus:border-brand-blue-200">
                        <span className="absolute left-8 top-2 gap-1 flex text-brand-black-200 text-sm">
                            <span>
                                {selectedLocation ? "Location" : "Zip Code"}
                            </span>
                            <span>
                                <SearchSmallIcon />
                            </span>
                        </span>
                        <input
                            type="text"
                            value={selectedLocation || zipcode}
                            placeholder={isLoadingLocation ? "Getting your location..." : selectedLocation ? selectedLocation : "Enter zip code"}
                            onChange={(e) => handleZipcodeChange(e.target.value)}
                            className="w-full h-full border-none outline-none p-0"
                            readOnly={!!selectedLocation || isLoadingLocation}
                        />
                        {selectedLocation && (
                            <button
                                onClick={() => {
                                    setSelectedLocation("")
                                    setIsLocationSelected(false)
                                    setZipcode("")
                                }}
                                className="absolute right-4 top-6 cursor-pointer  text-red-500 hover:text-red-700"
                            >
                                ✕
                            </button>
                        )}
                    </div>

                    {/* Show loading when user is typing and searching */}
                    {!selectedLocation && isSearching && (
                        <div className="bg-white rounded-lg shadow-location-dropdown flex items-center justify-center h-60 mt-3">
                            <div className="flex items-center justify-center">
                                <div className="flex space-x-1">
                                    <div className="w-[10px] h-[10px] bg-brand-gray-970 rounded-full animate-pulse"></div>
                                    <div className="w-[10px] h-[10px] bg-brand-gray-970 rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-[10px] h-[10px] bg-brand-gray-970 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Show suggestions when available (and not searching) */}
                    {!selectedLocation && !isSearching && showSuggestions && suggestions.length > 0 && (
                        <div className="bg-white rounded-lg shadow-location-dropdown max-h-60 overflow-y-auto mt-3">
                            {suggestions.map((suggestion) => (
                                <div
                                    key={suggestion.place_id}
                                    onClick={() => handleSuggestionClick(suggestion)}
                                    className="flex rounded-lg items-center gap-3 px-4 py-3 hover:bg-brand-gray-600 cursor-pointer border-b border-gray-100 last:border-b-0"
                                >
                                    <div className="text-brand-gray-970">
                                        <LocationPinIcon />
                                    </div>
                                    <span className="text-brand-black-300 text-lg">{suggestion.description}</span>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* No results message */}
                    {!selectedLocation && !isSearching && showSuggestions && suggestions.length === 0 && zipcode.length >= 3 && (
                        <div className="bg-white rounded-lg shadow-location-dropdown flex items-center justify-center h-20 mt-3">
                            <span className="text-brand-gray-970 text-lg">No locations found for &quot;{zipcode}&quot;</span>
                        </div>
                    )}

                    {/* Empty state - show when not loading, not showing suggestions, and not loading location */}
                    {(!isSearching && !showSuggestions && !isLoadingLocation) && (
                        <div className="h-60 mt-3" />
                    )}
                </div>
            </div>

            {/* Loading current location */}
            {isLoadingLocation && (
                <div className="mb-4 bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center gap-3">
                        <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-gray-600">Getting your current location...</span>
                    </div>
                </div>
            )}

            <div className="mt-3">
                <button
                    onClick={handleConfirm}
                    disabled={!isButtonEnabled}
                    className={`w-full h-[60px] text-white rounded-lg cursor-pointer text-lg font-medium transition-colors ${isButtonEnabled
                        ? 'bg-background-custom3 text-white hover:bg-brand-blue-400'
                        : 'bg-background-disabled cursor-not-allowed'
                        }`}
                >
                    Confirm & continue
                </button>
            </div>
        </div>
    )
} 