"use client"

import { <PERSON>actN<PERSON>, useEffect, useRef, useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface HorizontalSliderProps {
    children: ReactNode
    itemGap?: string // tailwind gap classes
}

export default function HorizontalSlider({ children, itemGap = "gap-x-10" }: HorizontalSliderProps) {
    const containerRef = useRef<HTMLDivElement>(null)
    const [showLeft, setShowLeft] = useState(false)
    const [showRight, setShowRight] = useState(false)

    const updateArrows = () => {
        const el = containerRef.current
        if (!el) return
        setShowLeft(el.scrollLeft > 0)
        setShowRight(el.scrollWidth - el.clientWidth - el.scrollLeft > 1)
    }

    useEffect(() => {
        updateArrows()
        const el = containerRef.current
        if (!el) return
        el.addEventListener("scroll", updateArrows)
        window.addEventListener("resize", updateArrows)
        return () => {
            el.removeEventListener("scroll", updateArrows)
            window.removeEventListener("resize", updateArrows)
        }
    }, [])

    const scrollAmount = () => {
        const el = containerRef.current
        if (!el) return 0
        return el.clientWidth // scroll a viewport width
    }

    const scrollLeftHandler = () => {
        const el = containerRef.current
        if (!el) return
        el.scrollBy({ left: -scrollAmount(), behavior: "smooth" })
    }
    const scrollRightHandler = () => {
        const el = containerRef.current
        if (!el) return
        el.scrollBy({ left: scrollAmount(), behavior: "smooth" })
    }

    return (
        <div className="relative w-full flex-1">
            {showLeft && (
                <button
                    onClick={scrollLeftHandler}
                    className="absolute left-0 top-1/2 -translate-y-1/2 bg-white shadow rounded-full p-1 z-10"
                >
                    <ChevronLeft className="w-5 h-5" />
                </button>
            )}
            {showRight && (
                <button
                    onClick={scrollRightHandler}
                    className="absolute right-0 top-1/2 -translate-y-1/2 bg-white shadow rounded-full p-1 z-10"
                >
                    <ChevronRight className="w-5 h-5" />
                </button>
            )}
            <div
                ref={containerRef}
                className={`flex ${itemGap} overflow-x-auto no-scroll scrollbar-hide px-4`}
            >
                {children}
            </div>
        </div>
    )
} 