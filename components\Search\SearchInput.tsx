import { Search } from "lucide-react";

export default function SearchInput() {
    return (
        <div className="flex items-center gap-2 relative w-[525px] mx-auto">
            <input type="text" placeholder="Current location" className="w-full h-[48px] border border-brand-black-150 rounded-full pr-4 pl-12 py-2 " />
            <Search className="w-6 h-6 absolute left-4 top-1/2 -translate-y-1/2 text-brand-gray-400" />
        </div>
    )
}