"use client"

import { Minus, Plus } from "lucide-react"
import { useState } from "react"

interface QuantitySelectorProps {
    value: number
    onChange?: (value: number) => void
    min?: number
    max?: number
}

export default function QuantitySelector({ value, onChange, min = 1, max = 99 }: QuantitySelectorProps) {
    const [internalVal, setInternalVal] = useState<number>(value)

    const updateValue = (newVal: number) => {
        const clamped = Math.min(Math.max(newVal, min), max)
        setInternalVal(clamped)
        onChange?.(clamped)
    }

    const handleIncrement = () => updateValue(internalVal + 1)
    const handleDecrement = () => updateValue(internalVal - 1)

    return (
        <div className="px-8 py-6">
            <p className="text-brand-100 text-xl font-medium mb-6">Set amount</p>
            <div className="flex items-center gap-4 mb-3">
                <div className="flex items-center border rounded-md px-3 py-2 gap-4 w-[132px] justify-between">
                    <div className="w-7 h-7 rounded-full hover:bg-brand-gray-800 transition-colors cursor-pointer flex items-center justify-center" onClick={handleDecrement}>
                        <Minus className="w-4 h-4" />
                    </div>
                    <span className="w-4 text-center select-none">{internalVal}</span>
                    <div className="w-7 h-7 rounded-full hover:bg-brand-gray-800 transition-colors cursor-pointer flex items-center justify-center" onClick={handleIncrement}>
                        <Plus className="w-4 h-4" />
                    </div>
                </div>
            </div>
            <p className="text-brand-gray-550 text-sm">At least {internalVal} or more available</p>
        </div>
    )
} 