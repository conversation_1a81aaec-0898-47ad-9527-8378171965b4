import Image from "next/image"
import { PhoneIcon2 } from "../Icons"

interface AlreadySignupPhoneScreenProps {
    phone: string
    onSendCode: () => void
    onUseAnother: () => void
}

export default function AlreadySignupPhoneScreen({
    phone,
    onSendCode,
    onUseAnother
}: AlreadySignupPhoneScreenProps) {
    return (
        <div className="px-8 pb-8 pt-4">
            <div className="text-center">
                <div className="mb-8">
                    <div className="flex justify-center mb-6">
                        <Image
                            src="/images/Idea4.png"
                            alt="Profile"
                            width={180}
                            height={180}
                            className="rounded-full w-[180px] h-[180px] object-cover"
                        />
                        {/* <div className="w-[120px] h-[120px] rounded-full bg-black flex items-center justify-center">
                            <div className="w-[80px] h-[80px] rounded-full bg-white flex items-center justify-center">
                            </div>
                        </div> */}
                    </div>

                    <div className="flex items-center justify-center gap-2 text-black mb-20">
                        <span><PhoneIcon2 /></span>
                        <span className="text-lg">
                            {phone.length > 4
                                ? '•'.repeat(phone.length - 4) + phone.slice(-4)
                                : phone.replace(/./g, '•')
                            }
                        </span>
                    </div>

                    <button
                        onClick={onSendCode}
                        className="w-full h-[60px] border text-black border-brand-gray-990 flex items-center justify-center gap-2 rounded-lg text-lg relative cursor-pointer mb-4"
                    >
                        <span className="absolute left-4">
                            <PhoneIcon2 />
                        </span>
                        <span>
                            Send code
                        </span>
                    </button>

                    <div className="text-left text-black">
                        <span>Not you? </span>
                        <button
                            onClick={onUseAnother}
                            className="font-medium underline cursor-pointer"
                        >
                            Use another account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
} 