"use client"

import Link from "next/link"
import { HomeIcon } from "lucide-react"
import Header from "@/components/Sections/Header"
import FooterSection from "@/components/Sections/FooterSection"
import Image from "next/image"


export default function NotFound() {
    return (
        <>
            <Header />
            <div className="bg-brand-100 fixed top-0 left-0 w-full h-[100px] z-10"></div>
            <div className="px-10 relative pb-32 pt-32">
                <h2 className="text-brand-600 font-extrabold text-[245px] ">
                    404
                </h2>
                <h3 className="font-bold text-brand-600 text-4xl leading-[140%] tracking-[-1%] mb-6">
                    Oops! You&apos;ve landed on the <br /> wrong page!
                </h3>
                <p className="text-brand-200 text-xl mb-10">
                    Looks like this page doesnt lead anywhere... <br /> lets go back home.
                </p>
                <Link href="/" className="bg-brand-600 tracking-[-1%] text-white px-16 py-4 rounded-md shadow-customBox3 inline-flex items-center gap-2 ">
                    <HomeIcon /> Home
                </Link>
                <Image src="/images/404-not-found.png" alt="404-not-found" width={1000} height={1000} className="absolute bottom-0 right-0" />
            </div>
            <FooterSection />
        </>
    )
}
