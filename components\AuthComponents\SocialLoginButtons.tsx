import { FacebookIcon2, GoogleLoginIcon, MailIcon, PhoneIcon } from "../Icons";

interface SocialLoginButtonsProps {
    onEmailToggle?: () => void;
    isEmailMode?: boolean;
}

export default function SocialLoginButtons({ onEmailToggle, isEmailMode }: SocialLoginButtonsProps) {
    return (
        <>
            <button className="mb-6 border border-brand-gray-990 rounded-xl text-black text-xl w-full py-5 flex items-center justify-center cursor-pointer relative">
                <span className="absolute left-7 top-1/2 -translate-y-1/2">
                    <GoogleLoginIcon />
                </span>
                <span>
                    Continue with Google
                </span>
            </button>
            <button className="mb-6 bg-black rounded-xl text-white text-xl w-full py-5 flex items-center justify-center cursor-pointer relative">
                <span className="absolute left-7 top-1/2 -translate-y-1/2">
                    <span className="w-[32px] h-[32px] rounded-md bg-brand-1500 flex"></span>
                </span>
                <span>
                    Continue with Apple
                </span>
            </button>
            <button className="mb-6 bg-brand-facebook rounded-xl text-white text-xl w-full py-5 flex items-center justify-center cursor-pointer relative">
                <span className="absolute left-7 top-1/2 -translate-y-1/2">
                    <FacebookIcon2 />
                </span>
                <span>
                    Continue with Facebook
                </span>
            </button>
            <button
                onClick={onEmailToggle}
                className="bg-brand-gray-995 rounded-xl text-white text-xl w-full py-5 flex items-center justify-center cursor-pointer relative">
                <span className="absolute left-7 top-1/2 -translate-y-1/2">
                    {isEmailMode ? <PhoneIcon /> : <MailIcon />}
                </span>
                <span>
                    {isEmailMode ? "Continue with phone" : "Continue with email"}
                </span>
            </button>
        </>
    )
}