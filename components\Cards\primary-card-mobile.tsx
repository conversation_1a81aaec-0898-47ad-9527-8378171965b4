"use client";

import React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface PrimaryCardMobileProps {
    image: string;
    price: number;
    priceUnit?: string;
    rating: number;
    title: string;
    deliveryIncluded?: boolean;
    className?: string;
}

// Rating Star Component
const RatingStar = ({ rating }: { rating: number }) => {
    if (rating === 0) {
        return <Star size={14} className="fill-gray-400 text-gray-400" />;
    } else if (rating < 4.0) {
        return (
            <div className="relative">
                <Star size={14} className="fill-gray-400 text-gray-400" />
                <div className="absolute inset-0 overflow-hidden w-1/2">
                    <Star size={14} className="fill-brand-500 text-brand-500" />
                </div>
            </div>
        );
    } else {
        return <Star size={14} className="fill-brand-500 text-brand-500" />;
    }
};

export function PrimaryCardMobile({
    image,
    price,
    priceUnit = "day",
    rating,
    title,
    className,
    deliveryIncluded = false,
}: PrimaryCardMobileProps) {


    return (
        <div className={cn("", className)}>
            {/* Image Section */}
            <div className="relative ">
                <Image
                    src={image}
                    alt={title}
                    className="w-full h-[193] object-cover border border-brand-700 rounded-sm"
                    width={193}
                    height={193}
                />

                <div className="absolute top-2 right-2 flex items-center gap-1 px-2 py-1 bg-brand-400 rounded-full">
                    <RatingStar rating={rating} />
                    <span className="text-sm font-medium text-brand-100">
                        {rating === 0 ? "0" : rating.toFixed(1)}
                    </span>
                </div>

                {deliveryIncluded && (
                    <div className="absolute bottom-1 left-1 rounded-md px-3 py-1 bg-brand-900 text-white text-xs font-medium">
                        Delivery
                    </div>
                )}
            </div>

            {/* Content Section */}
            <div className="flex-1 py-2 min-w-0">
                {/* Price and Title on one line */}
                <div className="text-black text-base flex items-center gap-1">
                    <span className="flex-shrink-0">${price} / {priceUnit} •</span>
                    <span className="truncate">{title}</span>
                </div>
            </div>
        </div>
    );
} 