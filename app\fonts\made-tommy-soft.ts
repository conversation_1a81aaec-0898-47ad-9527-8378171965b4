import localFont from 'next/font/local';

export const madeTommySoft = localFont({
  src: [
    {
      path: '../../public/fonts/made_tommy_soft_light_personal_use-webfont.woff2',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/made_tommy_soft_regular_personal_use-webfont.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/made_tommy_soft_medium_personal_use-webfont.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/made_tommy_soft_bold_personal_use-webfont.woff2',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/made_tommy_soft_extrabold_personal_use-webfont.woff2',
      weight: '800',
      style: 'normal',
    },
    {
      path: '../../public/fonts/made_tommy_soft_black_personal_use-webfont.woff2',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-made-tommy-soft',
  display: 'swap',
  fallback: ['system-ui', 'arial'],
}); 