import NoItemListed from '@/components/RentalManager/NoItemListed'
import NotificationCard from '@/components/RentalManager/NotificationCard'
import YourRentalsSection from '@/components/RentalManager/YourRentalsSection'
import { notificationCardData } from '@/data'
import Link from 'next/link'
import React from 'react'

export default function RentalManagerOwnerPage() {
    return (
        <>
            <div className="pt-28">
                <div className="container inner-page-container mx-auto pb-10">
                    <NoItemListed />
                </div>
                <div className="container inner-page-container mx-auto">
                    <h2 className="text-black text-4xl font-medium mb-10">Welcome back, Eliya</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-4 md:grid-cols-2 gap-6">
                        {notificationCardData.map((item) => (
                            <NotificationCard
                                key={item.id}
                                title={item.title}
                                status={item.status}
                                date={item.date}
                                itemImage={item.itemImage}
                                name={item.name}
                                size={item.size}
                                link={item.link}
                                btnText={item.btnText}
                            />
                        ))}
                    </div>
                    {
                        notificationCardData.length > 7 && (
                            <div className="text-right mt-8">
                                <Link href={"#"} className="underline text-lg">
                                    Show more
                                </Link>
                            </div>
                        )
                    }
                </div>
                <div className="container inner-page-container mx-auto">
                    <YourRentalsSection />
                </div>
            </div>
        </>
    )
}
