import { PrimaryButton } from "../Buttons";
import { PrimaryCard } from "../Cards";
import CategorySlider from "../CategorySlider";
import { AdventureIcon, AttireIcon, ToolsIcon, ProductionIcon, SearchIcon2, SpacesIcon, SportsIcon } from "../Icons";
import Filters from "../Search/Filters";
import FiltersModal from "../Search/FiltersModal";

import { primaryCardData } from "@/data";

export default function ListingComponentHome() {
    return (
        <>
            <div className="flex py-3 gap-x-14 items-center px-10 border-b border-brand-gray-850 justify-center">
                <div className="w-[8%]">
                    <div className="flex flex-col items-center justify-center search-filter relative ">
                        <SearchIcon2 />
                        <p className="text-brand-600">All rentals</p>
                    </div>
                </div>
                <div className="flex-1 w-[80%]">
                    <CategorySlider>
                        <Filters Icon={SpacesIcon} text="Spaces" />
                        <Filters Icon={AdventureIcon} text="Adventure" />
                        <Filters Icon={AttireIcon} text="Attire" />
                        <Filters Icon={ProductionIcon} text="Production" />
                        <Filters Icon={ToolsIcon} text="Tools" />
                        <Filters Icon={SportsIcon} text="Sports" />
                        <Filters Icon={SportsIcon} text="Electronics" />
                        <Filters Icon={SportsIcon} text="Furniture" />
                        <Filters Icon={SportsIcon} text="Animals" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                        <Filters Icon={SportsIcon} text="Kids & babies" />
                    </CategorySlider>
                </div>
                <div className="text-center w-[8%]">
                    <FiltersModal />
                </div>
            </div>

            <div className="px-6 pt-14">
                {/* Mobile Card Demo Section */}
                {/* <div className="mb-8">
                    <div className="grid grid-cols-2 gap-2">
                        {mobileCardData.map((item, idx) => (
                            <PrimaryCardMobile key={idx} {...item} />
                        ))}
                    </div>

                </div> */}

                {/* <h2 className="text-2xl font-semibold mb-6">Desktop Card Layout</h2> */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
                    {primaryCardData.map((item, idx) => (
                        <PrimaryCard key={idx} {...item} />
                    ))}
                </div>
                <div className="text-center mt-24">
                    <PrimaryButton btnText="Show all" href="#" />
                </div>
            </div>
        </>
    )
}