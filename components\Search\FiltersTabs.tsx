import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Truck, Users } from "lucide-react"
import SearchInput from "./SearchInput"
import RangeSlider from "./RangeSlider"
import { FilterCard } from "../Cards"

interface FiltersTabsProps {
    onSelectFilter: (filterName: string) => void;
    selectedValues: Record<string, string>;
    activeTab: string;
    onTabChange: (value: string) => void;
}

export default function FiltersTabs({ onSelectFilter, selectedValues, activeTab, onTabChange }: FiltersTabsProps) {
    const getOption = (name: string, fallback: string) => selectedValues[name] ?? fallback;
    return (
        <Tabs value={activeTab} onValueChange={onTabChange}>
            <div className="px-8">
                <TabsList className="border border-black border-opacity-90 w-full p-1 flex h-auto bg-white">
                    <TabsTrigger value="localDelivery" className="cursor-pointer flex data-[state=active]:text-black data-[state=active]:border-2 data-[state=active]:border-black px-6 py-2 font-medium">Local + Delivery</TabsTrigger>
                    <TabsTrigger value="localPickup" className="cursor-pointer flex data-[state=active]:text-black data-[state=active]:border-2 data-[state=active]:border-black px-6 py-2 font-medium"><Users /> Local pick up</TabsTrigger>
                    <TabsTrigger value="delivery" className="cursor-pointer flex data-[state=active]:text-black data-[state=active]:border-2 data-[state=active]:border-black px-6 py-2 font-medium"><Truck /> Delivery</TabsTrigger>
                </TabsList>
            </div>

            <div className="w-full h-[1px] bg-brand-black-150 mb-6 mt-4"></div>
            <TabsContent value="localDelivery">
                <div className="px-8">
                    <p className="text-brand-100 text-xl font-medium mb-5">Location</p>
                    <SearchInput />
                </div>
                <div className="w-full h-[1px] bg-brand-black-150 mb-6 mt-5"></div>
                <div className="px-8">
                    <p className="text-brand-100 text-xl font-medium mb-12">Local distance</p>
                    <RangeSlider min={0} max={100} defaultValue={30} step={1} unit="mi" />
                </div>
                <div className="pt-12">
                    <FilterCard name="Sort by" option={getOption("Sort by", "Suggested")} onClick={() => onSelectFilter("Sort by")} />
                    <FilterCard name="Price" option={getOption("Price", "Any")} onClick={() => onSelectFilter("Price")} />
                    <FilterCard name="Category" option={getOption("Category", "Any")} onClick={() => onSelectFilter("Category")} />
                    <FilterCard name="Rental rate" option={getOption("Rental rate", "Hour + Day")} onClick={() => onSelectFilter("Rental rate")} />
                    <FilterCard name="Quantity available" option={getOption("Quantity available", "1 or more")} onClick={() => onSelectFilter("Quantity available")} />
                </div>
            </TabsContent>
            <TabsContent value="localPickup">
                <div className="px-8">
                    <p className="text-brand-100 text-xl font-medium mb-5">Location</p>
                    <SearchInput />
                </div>
                <div className="w-full h-[1px] bg-brand-black-150 mb-6 mt-5"></div>
                <div className="px-8">
                    <p className="text-brand-100 text-xl font-medium mb-12">Local distance</p>
                    <RangeSlider min={0} max={100} defaultValue={30} step={1} unit="mi" />
                </div>
                <div className="pt-12">
                    <FilterCard name="Sort by" option={getOption("Sort by", "Suggested")} onClick={() => onSelectFilter("Sort by")} />
                    <FilterCard name="Price" option={getOption("Price", "Any")} onClick={() => onSelectFilter("Price")} />
                    <FilterCard name="Category" option={getOption("Category", "Any")} onClick={() => onSelectFilter("Category")} />
                    <FilterCard name="Rental rate" option={getOption("Rental rate", "Hour + Day")} onClick={() => onSelectFilter("Rental rate")} />
                    <FilterCard name="Quantity available" option={getOption("Quantity available", "1 or more")} onClick={() => onSelectFilter("Quantity available")} />
                </div>
            </TabsContent>
            <TabsContent value="delivery">Delivery.</TabsContent>
        </Tabs>
    )
}