interface DeclineConfirmationModalProps {
    onGoBack: () => void
    onCancelSignup: () => void
}

export default function DeclineConfirmationModal({ onGoBack, onCancelSignup }: DeclineConfirmationModalProps) {
    return (
        <div className="px-8 pb-8">
            <div className="mb-20">
                <div className="text-left">
                    <h3 className="text-brand-black-300 text-2xl font-medium mb-10">Are you sure?</h3>

                    <div className="space-y-4">
                        <p className="text-brand-black-200 text-base">
                            By declining, you won&apos;t be able to use YouHook. Agreeing to our{' '}
                            <a href="#" className="text-brand-blue-200 underline">Community Guidelines</a>
                            {' '}and{' '}
                            <a href="#" className="text-brand-blue-200 underline">Terms of Service</a>
                            {' '}is required to signup to our platform and ensures a safe and trusted experience for all users.
                        </p>

                        <p className="text-brand-black-200 text-base">
                            You can always come back if you change your mind later.
                        </p>
                    </div>
                </div>
            </div>

            <div className="space-y-3">
                <button
                    onClick={onGoBack}
                    className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium hover:bg-brand-black-400 transition-colors cursor-pointer"
                >
                    Go back
                </button>

                <button
                    onClick={onCancelSignup}
                    className="w-full h-[60px] border border-brand-gray-960 text-brand-black-200 rounded-lg text-lg font-medium hover:bg-brand-gray-600 transition-colors cursor-pointer"
                >
                    Cancel signup
                </button>
            </div>
        </div>
    )
} 