"use client"
import { BellIcon } from "lucide-react";
import Link from "next/link";
import HeaderDropdown from "./HeaderDropdown";

export default function Header() {
    return (
        <header className="fixed top-0 left-0 right-0 z-50 w-full bg-transparent">
            <div className="px-10 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 justify-between w-full">
                        <Link href={"/"} className="text-2xl font-extrabold text-white text-[42px]">
                            YouHook
                        </Link>
                        <div className="flex items-center gap-4 ">
                            <button className="text-white  font-medium">
                                <BellIcon />
                            </button>
                            <Link href={""} className="text-white  font-medium">
                                Sign up
                            </Link>
                            <HeaderDropdown />
                        </div>
                    </div>
                </div>
            </div>
        </header>
    )
}