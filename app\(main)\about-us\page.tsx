import { TeamCard } from "@/components/Cards";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function AboutUs() {
    return (
        <>

            <div className="bg-brand-100 fixed top-0 left-0 w-full h-[100px] z-10"></div>
            <div className="mt-[200px] text-center">
                <h2 className="text-brand-200 font-light text-3xl mb-3">About YouHook</h2>
                <h1 className="font-medium text-brand-600 text-5xl">Redefining the Marketplace</h1>
            </div>
            <div className="container inner-page-container mx-auto mb-20">
                <div className="border-l-2 border-b-2 border-brand-1100 relative rounded-4xl rounded-br-none">
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute top-0 -left-[22px]" />
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute bottom-[-25px] right-[-12px]" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-[100px] pt-20 gap-x-20 items-center pb-20">
                        <div>
                            <h1 className="font-bold text-brand-600 text-2xl mb-3">Redefining the Marketplace</h1>
                            <p className="text-black text-xl tracking-[-1.1%] leading-[150%]">
                                YouHook began as a solution to a glaring problem: selling your items kills their income potential. In 2022, right after COVID, its founders were using platforms like OfferUp and Facebook Marketplace to make money, but they realized how limiting it was to sell items outright. Once sold, those items were gone forever—along with any future earnings. They thought: Why sell when you can rent? That’s when the concept for a peer-to-peer rental marketplace was born.
                            </p>
                        </div>
                        <div>
                            <Image src="/images/about-1.png" alt="About Us" width={514} height={325} />
                        </div>
                    </div>
                </div>
                <div className="border-r-2 border-b-2 border-brand-1100 relative rounded-4xl rounded-bl-none">
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute bottom-[-30px] left-[-10px]" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-[100px] pt-20 gap-x-20 items-center pb-20">
                        <div>
                            <Image src="/images/about-2.png" alt="About Us" width={514} height={325} />
                        </div>
                        <div>
                            <h1 className="font-bold text-brand-600 text-2xl mb-3">The problem:  Trust was missing</h1>
                            <p className="text-black text-xl tracking-[-1.1%] leading-[150%]">
                                After diving into research, they discovered the biggest reason people don’t rent out their belongings: they don’t trust strangers with their stuff. The team looked at competitors to see how they were addressing this issue, but none of them were doing a good job.
                            </p>
                        </div>

                    </div>
                </div>
                <div className="border-l-2 border-b-2 border-brand-1100 relative rounded-4xl rounded-br-none">
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute bottom-[-26px] -right-[12px]" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-[100px] pt-20 gap-x-20 items-center pb-20">
                        <div>
                            <h1 className="font-bold text-brand-600 text-2xl mb-3">The solution: Building a safer system</h1>
                            <p className="text-black text-xl tracking-[-1.1%] leading-[150%]">
                                So, they took matters into their own hands and built YouHook. Countless hours were spent testing and developing a robust system to prevent theft, damage, and bad actors. That’s why they created features like the Advanced Rental Manager, AI Integration, Real-Time Rental Tracking, Security Deposits, YouHook Coverage, and Verified Users—all designed to make every rental safe, secure, and hassle-free.
                            </p>
                        </div>
                        <div>
                            <Image src="/images/about-3.png" alt="About Us" width={514} height={325} />
                        </div>
                    </div>
                </div>
                <div className="border-r-2 border-b-2 border-brand-1100 relative rounded-4xl rounded-bl-none">
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute bottom-[-26px] left-[-12px]" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-[100px] pt-20 gap-x-20 items-center pb-20">
                        <div>
                            <Image src="/images/about-4.png" alt="About Us" width={514} height={325} />
                        </div>
                        <div>
                            <h1 className="font-bold text-brand-600 text-2xl mb-3">The Impact: Safe Rentals</h1>
                            <p className="text-black text-xl tracking-[-1.1%] leading-[150%]">
                                The results? A safe way to actually rent anything.
                                <br />
                                <br />
                                The team behind YouHook genuinely believes that creating a centralized space where businesses and individuals can rent items isn’t just good for the economy—it’s good for the planet. It reduces waste, saves money, and makes money. It’s a win-win-win.
                            </p>
                        </div>
                    </div>
                </div>
                <div className="border-l-2 border-b-2 border-brand-1100 relative rounded-4xl rounded-br-none">
                    <div className="w-[44px] h-[44px] rounded-full bg-brand-1100  absolute bottom-[-20px] -right-[12px]" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-[100px] pt-20 gap-x-20 items-center pb-20">
                        <div>
                            <h1 className="font-bold text-brand-600 text-2xl mb-3">The promise: Zero fees, zero nonsense</h1>
                            <p className="text-black text-xl tracking-[-1.1%] leading-[150%]">
                                To prove they truly believe in this idea—and in the people using it—they’ve implemented zero fees. That’s right, no fees. They keep costs low by cutting out corporate nonsense. No financial meetings, no HR departments, and no 15-person committees to get one thing done. YouHook is run by a small team of highly motivated, highly driven individuals who get the job  done efficiently.
                            </p>
                        </div>
                        <div>
                            <Image src="/images/about-5.png" alt="About Us" width={514} height={325} />
                        </div>
                    </div>
                </div>
            </div>
            <div className="container mx-auto">
                <p className="text-black text-2xl leading-[150%] tracking-[-1.1%] text-center px-16 mb-32">
                    At its core, YouHook isn’t just building a rental platform—it’s building a movement. A movement that reduces waste, saves money, and makes money. It’s a call to join in creating a smarter, greener future.
                </p>
                <h2 className="text-brand-600 text-5xl font-medium text-center mt-10 tracking-[-1.1%] mb-22">
                    Meet the Team
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-22">
                    <TeamCard image="/images/daniel.png" name="Daniel" title="CEO & Founder" />
                    <TeamCard image="/images/eliya.png" name="Eliya" title="Marketing Chairman & Co Founder" />
                    <TeamCard image="/images/micheal.png" name="Micheal" title="Logics Operator" />
                    <TeamCard image="/images/insert-name.png" name="Insert name" title="Development" />
                    <TeamCard image="/images/sherman.png" name="Sherman" title="Mascot & Support Manager" />
                    <div className="flex flex-col items-center gap-4 self-center">
                        <div className="w-[149px] h-[149px] bg-white border border-brand-gray-980 rounded-full shadow-customBox2 flex items-center justify-center text-center">
                            <span className="font-light text-2xl tracking-[-1%]">
                                Want to <span className="gradient-text">join?</span>
                            </span>
                        </div>
                        <Link href="/contact-us" className=" gap-2 font-medium text-brand-600 tracking-[-1%] text-center ">
                            Learn more about jobs <br /> at YouKook
                            <ChevronRight className="inline-block ml-2 " />
                        </Link>
                    </div>
                </div>
            </div>
        </>
    )
}