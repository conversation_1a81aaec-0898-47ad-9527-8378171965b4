import Link from "next/link";
import { DropdownMenuItem } from "../ui/dropdown-menu";

export function HeaderDropdownLink({ href, text, onClick, count }: { href: string, text: string, onClick?: () => void, count?: number }) {
    return (
        <DropdownMenuItem onClick={onClick} className="hover:bg-brand-gray-900 px-6 py-3">
            <Link href={href} className="text-black font-normal w-full text-lg flex items-center justify-between gap-2">
                {text}
                <span>
                    {count && <span className="text-white text-sm font-medium w-[30px] h-[30px] bg-brand-red-500 rounded-full flex items-center justify-center">
                        {count}
                        <span>
                            {count > 8 ? "+" : ""}
                        </span>

                    </span>}
                </span>
            </Link>
        </DropdownMenuItem>
    )
}