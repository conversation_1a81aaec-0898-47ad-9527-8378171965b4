/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on July 10, 2025 */



@font-face {
    font-family: 'made_tommy_soft_outlineblack';
    src: url('made_tommy_soft_black_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_black_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softblack';
    src: url('made_tommy_soft_black_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_black_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_soft_outlinebold';
    src: url('made_tommy_soft_bold_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_bold_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softbold';
    src: url('made_tommy_soft_bold_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_bold_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_soft_outlineXBd';
    src: url('made_tommy_soft_extrabold_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_extrabold_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softextrabold';
    src: url('made_tommy_soft_extrabold_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_extrabold_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_soft_outlinelight';
    src: url('made_tommy_soft_light_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_light_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softlight';
    src: url('made_tommy_soft_light_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_light_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_soft_outlinemedium';
    src: url('made_tommy_soft_medium_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_medium_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softmedium';
    src: url('made_tommy_soft_medium_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_medium_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_soft_outlineRg';
    src: url('made_tommy_soft_regular_outline_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_regular_outline_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'made_tommy_softregular';
    src: url('made_tommy_soft_regular_personal_use-webfont.woff2') format('woff2'),
         url('made_tommy_soft_regular_personal_use-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}