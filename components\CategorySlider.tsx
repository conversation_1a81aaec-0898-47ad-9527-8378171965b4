"use client"

import { <PERSON>actN<PERSON>, useRef, useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Swiper, SwiperSlide } from "swiper/react"
import { Navigation } from "swiper/modules"
import type { Swiper as SwiperInstance } from "swiper/types"
import "swiper/css";
import "swiper/css/navigation";

interface CategorySliderProps {
    children: ReactNode[]
    spaceBetween?: number
}

export default function CategorySlider({ children, spaceBetween = 40 }: CategorySliderProps) {
    const swiperRef = useRef<SwiperInstance | null>(null)
    const [isBeginning, setIsBeginning] = useState(true)
    const [isEnd, setIsEnd] = useState(false)

    const handleSwiper = (swiper: SwiperInstance) => {
        swiperRef.current = swiper
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
    }

    const handleUpdate = (swiper: SwiperInstance) => {
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
    }

    const slidePrev = () => swiperRef.current?.slidePrev()
    const slideNext = () => swiperRef.current?.slideNext()

    return (
        <div className="relative w-full flex-1">
            <div className="flex items-center gap-x-2">
                {!isBeginning && (
                    <div>
                        <button onClick={slidePrev} className="cursor-pointer bg-white shadow p-0 rounded-full w-[34px] h-[34px] border border-brand-gray-450 flex items-center justify-center">
                            <ChevronLeft className="w-5 h-5" />
                        </button>
                    </div>
                )}
                <Swiper
                    modules={[Navigation]}
                    onSwiper={handleSwiper}
                    onSlideChange={handleUpdate}
                    slidesPerView="auto"
                    spaceBetween={spaceBetween}
                    navigation={false}
                    className=""
                >
                    {children.map((child, idx) => (
                        <SwiperSlide key={idx} className="!w-auto">
                            {child}
                        </SwiperSlide>
                    ))}
                </Swiper>
                {!isEnd && (
                    <div>
                        <button onClick={slideNext} className="cursor-pointer bg-white shadow p-0 rounded-full w-[34px] h-[34px] border border-brand-gray-450 flex items-center justify-center">
                            <ChevronRight className="w-5 h-5" />
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
} 