"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface BookingCardProps {
    price: number;
    priceUnit: string;
    className?: string;
}

export default function BookingCard({
    price,
    priceUnit,
    className
}: BookingCardProps) {
    const [startDate, setStartDate] = useState("08/13/2024");
    const [endDate, setEndDate] = useState("08/13/2024");
    const [startTime, setStartTime] = useState("Add time");
    const [endTime, setEndTime] = useState("Add time");

    const handleRequestRental = () => {
        // Handle rental request
        console.log("Rental requested:", {
            startDate,
            endDate,
            startTime,
            endTime
        });
    };

    return (
        <div className={cn("bg-white border rounded-2xl p-6 shadow-custom-8 border-black/15 ", className)}>
            {/* Price */}
            <div className="mb-6">
                <div className="flex items-baseline gap-1">
                    <span className="text-3xl font-medium text-black">${price}</span>
                    <span className="text-brand-gray-1005 text-2xl">/ {priceUnit}</span>
                </div>
            </div>

            {/* Date Selection */}
            <div className="space-y-4 mb-6">
                {/* Start Date */}
                <div>
                    <label className="block text-sm font-medium text-brand-600 mb-2">
                        START DATE
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                        <div className="relative">
                            <select
                                value={startDate}
                                onChange={(e) => setStartDate(e.target.value)}
                                className="w-full px-3 py-3 border border-brand-gray-350 rounded-lg appearance-none bg-white text-brand-600 focus:outline-none focus:ring-2 focus:ring-brand-1000 focus:border-transparent"
                            >
                                <option value="08/13/2024">08/13/2024</option>
                                <option value="08/14/2024">08/14/2024</option>
                                <option value="08/15/2024">08/15/2024</option>
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-gray-1005 pointer-events-none" size={16} />
                        </div>
                        <div className="relative">
                            <select
                                value={startTime}
                                onChange={(e) => setStartTime(e.target.value)}
                                className="w-full px-3 py-3 border border-brand-gray-350 rounded-lg appearance-none bg-white text-brand-gray-1005 focus:outline-none focus:ring-2 focus:ring-brand-1000 focus:border-transparent"
                            >
                                <option value="Add time">Add time</option>
                                <option value="09:00 AM">09:00 AM</option>
                                <option value="10:00 AM">10:00 AM</option>
                                <option value="11:00 AM">11:00 AM</option>
                                <option value="12:00 PM">12:00 PM</option>
                                <option value="01:00 PM">01:00 PM</option>
                                <option value="02:00 PM">02:00 PM</option>
                                <option value="03:00 PM">03:00 PM</option>
                                <option value="04:00 PM">04:00 PM</option>
                                <option value="05:00 PM">05:00 PM</option>
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-gray-1005 pointer-events-none" size={16} />
                        </div>
                    </div>
                </div>

                {/* End Date */}
                <div>
                    <label className="block text-sm font-medium text-brand-600 mb-2">
                        END DATE
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                        <div className="relative">
                            <select
                                value={endDate}
                                onChange={(e) => setEndDate(e.target.value)}
                                className="w-full px-3 py-3 border border-brand-gray-350 rounded-lg appearance-none bg-white text-brand-600 focus:outline-none focus:ring-2 focus:ring-brand-1000 focus:border-transparent"
                            >
                                <option value="08/13/2024">08/13/2024</option>
                                <option value="08/14/2024">08/14/2024</option>
                                <option value="08/15/2024">08/15/2024</option>
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-gray-1005 pointer-events-none" size={16} />
                        </div>
                        <div className="relative">
                            <select
                                value={endTime}
                                onChange={(e) => setEndTime(e.target.value)}
                                className="w-full px-3 py-3 border border-brand-gray-350 rounded-lg appearance-none bg-white text-brand-gray-1005 focus:outline-none focus:ring-2 focus:ring-brand-1000 focus:border-transparent"
                            >
                                <option value="Add time">Add time</option>
                                <option value="09:00 AM">09:00 AM</option>
                                <option value="10:00 AM">10:00 AM</option>
                                <option value="11:00 AM">11:00 AM</option>
                                <option value="12:00 PM">12:00 PM</option>
                                <option value="01:00 PM">01:00 PM</option>
                                <option value="02:00 PM">02:00 PM</option>
                                <option value="03:00 PM">03:00 PM</option>
                                <option value="04:00 PM">04:00 PM</option>
                                <option value="05:00 PM">05:00 PM</option>
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-gray-1005 pointer-events-none" size={16} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Request Button */}
            <button
                onClick={handleRequestRental}
                className="w-full bg-brand-gray-1003 hover:bg-brand-gray-1002 text-white py-4 rounded-lg font-medium transition-colors disabled:bg-brand-gray-1006  cursor-pointer disabled:cursor-not-allowed"
                disabled={startTime === "Add time" || endTime === "Add time"}
            >
                Request rental
            </button>
        </div>
    );
}
