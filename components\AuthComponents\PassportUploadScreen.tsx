import { useState } from "react"
import { Lock } from "lucide-react"
import Image from "next/image"
import { ReUploadButton } from "@/components/Buttons"

interface PassportUploadScreenProps {
    onContinue: () => void
    onBack?: () => void
}

export default function PassportUploadScreen({ onContinue }: PassportUploadScreenProps) {
    const [passportImage, setPassportImage] = useState<string | null>(null)

    const createFileInput = () => {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = 'image/*'
        input.style.display = 'none'

        input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0]
            if (file) {
                const reader = new FileReader()
                reader.onload = (e) => {
                    const result = e.target?.result as string
                    setPassportImage(result)
                }
                reader.readAsDataURL(file)
            }
            // Clean up
            if (input.parentNode) {
                input.parentNode.removeChild(input)
            }
        }

        document.body.appendChild(input)
        input.click()
    }

    const handleUploadPassport = () => {
        createFileInput()
    }

    const handleReUploadPassport = () => {
        setPassportImage(null)
        createFileInput()
    }

    const canContinue = passportImage !== null

    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-6">
                    Upload an image of your passport
                </h2>

                <p className="text-brand-black-300 text-lg mb-8">
                    Make sure the photo of your passport is not blurry and it clearly shows your face.
                </p>

                {/* Single Passport Upload */}
                <div className="mb-12 flex justify-center">
                    <div className="w-full">
                        {passportImage ? (
                            <div className="space-y-3">
                                <div className="w-full h-[250px] border py-3 border-brand-gray-1004 rounded-lg overflow-hidden">
                                    {/* eslint-disable-next-line @next/next/no-img-element */}
                                    <img
                                        src={passportImage}
                                        alt="Passport"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <ReUploadButton
                                    onClick={handleReUploadPassport}
                                    text="Re-upload passport"
                                />
                            </div>
                        ) : (
                            <div
                                onClick={handleUploadPassport}
                                className="w-full h-[250px] border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors text-center"
                            >
                                <div className="mb-4">
                                    <Image
                                        src="/images/passport_1.png"
                                        alt="Passport"
                                        width={60}
                                        height={60}
                                    />
                                </div>
                                <span className="text-brand-black-300 font-medium text-xl mb-1">Upload passport</span>
                                <span className="text-brand-gray-960 text-sm">JPEG or PNG</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Privacy Notice */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8 text-left">
                    <h4 className="font-medium text-brand-black-300 mb-2 flex items-center gap-2">
                        <Lock size={16} />
                        Your Privacy
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                        We take your privacy seriously. Your ID and data will be kept safe, secure
                        and will never be shared with anyone. <span className="underline cursor-pointer">Learn more.</span>
                    </p>
                </div>

                {/* Continue Button */}
                <div className="flex justify-center">
                    <button
                        onClick={onContinue}
                        disabled={!canContinue}
                        className={`w-full h-[60px] rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 ${canContinue
                            ? 'bg-brand-black-200 text-white cursor-pointer hover:bg-brand-black-300'
                            : 'bg-background-disabled text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        <Lock size={16} />
                        Continue
                    </button>
                </div>
            </div>
        </div>
    )
} 