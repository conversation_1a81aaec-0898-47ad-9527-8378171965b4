import Link from 'next/link';
import React from 'react'
import HeaderDropdown from './HeaderDropdown';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';

const RentalManagerHeader = () => {
    return (
        <header >
            <div className="flex items-center justify-between border-b border-brand-gray-850 px-6 py-2 fixed z-50 w-full bg-white">
                <Link href={"/"} className="text-2xl font-extrabold text-brand-blue-200/60 text-[42px]">
                    YouHook
                </Link>
                <div>
                    <ul className="flex gap-x-10">
                        <li>
                            <Link href={""} className={`${true ? 'text-black active-tab' : 'text-brand-gray-1012 '} font-medium`}>Today</Link>
                        </li>
                        <li>
                            <Link href={""} className={`${false ? 'text-black ' : 'text-brand-gray-1012 '} font-medium`}>Request</Link>
                        </li>
                        <li>
                            <Link href={""} className={`${false ? 'text-black ' : 'text-brand-gray-1012 '} font-medium`}>Calendar</Link>
                        </li>
                        <li>
                            <Link href={""} className={`${false ? 'text-black ' : 'text-brand-gray-1012 '} font-medium`}>Confirmed</Link>
                        </li>
                        <li>
                            <Link href={""} className={`${false ? 'text-black ' : 'text-brand-gray-1012 '} font-medium`}>History</Link>
                        </li>
                        <li>
                            <DropdownMenu>
                                <DropdownMenuTrigger className='cursor-pointer font-medium text-black flex'>
                                    Owner <ChevronDown className='text-brand-gray-1012' />
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                    <DropdownMenuItem>Profile</DropdownMenuItem>
                                    <DropdownMenuItem>Billing</DropdownMenuItem>
                                    <DropdownMenuItem>Team</DropdownMenuItem>
                                    <DropdownMenuItem>Subscription</DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </li>
                    </ul>
                </div>
                <HeaderDropdown isInnerpages={true} />
            </div>
        </header>
    )
}

export default RentalManagerHeader