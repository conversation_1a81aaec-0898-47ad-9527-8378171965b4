'use client'

import { Check, ChevronDown, ChevronUp } from 'lucide-react'
import { useRef, useState } from 'react'

const countries = [
    { name: "United States", code: "+1" },
    { name: "Uruguay", code: "+598" },
    { name: "Uzbekistan", code: "+998" },
    { name: "Vanuatu", code: "+678" },
    { name: "Vatican City", code: "+379" },
    { name: "Venezuela", code: "+58" },
    { name: "Vietnam", code: "+84" },
    { name: "Wallis & Futuna", code: "+681" },
    { name: "Western Sahara", code: "+212" },
    { name: "Yemen", code: "+967" },
    { name: "Zambia", code: "+260" },
    { name: "Zimbabwe", code: "+263" },
    // Add all other countries here...
]

export default function CountryCodeDropdown({ selected,
    onChange, }: {
        selected: { name: string; code: string }
        onChange: (val: { name: string; code: string }) => void
    }) {
    const [isOpen, setIsOpen] = useState(false)
    const listRef = useRef<HTMLDivElement>(null)
    const scrollInterval = useRef<NodeJS.Timeout | null>(null)

    const startScrolling = (direction: 'up' | 'down') => {
        if (scrollInterval.current) return
        scrollInterval.current = setInterval(() => {
            if (listRef.current) {
                listRef.current.scrollBy({
                    top: direction === 'down' ? 10 : -10,
                    behavior: 'auto',
                })
            }
        }, 50)
    }

    const stopScrolling = () => {
        if (scrollInterval.current) {
            clearInterval(scrollInterval.current)
            scrollInterval.current = null
        }
    }

    return (
        <div className={"relative px-3 py-2"}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full text-left flex flex-col gap-1 cursor-pointer relative"
            >
                <span className='text-sm text-brand-gray-970'>
                    Country code
                </span>
                <span className='text-black text-xl'>
                    {selected ? `${selected.name} (${selected.code})` : 'Select Country Code'}
                </span>
                <span className='absolute right-0 top-1/2 -translate-y-1/2'>
                    <ChevronDown size={20} />
                </span>
            </button>

            {isOpen && (
                <div className="absolute left-0 mt-1 z-50 w-full border rounded shadow-custom-black max-h-60 overflow-hidden bg-brand-gray-1000">
                    <div
                        onMouseEnter={() => startScrolling('up')}
                        onMouseLeave={stopScrolling}
                        className="flex justify-center items-center h-6 bg-gray-100 hover:bg-gray-200 cursor-pointer"
                    >
                        <ChevronUp size={18} />
                    </div>

                    <div className="overflow-y-auto max-h-48" ref={listRef}>
                        {countries.map((country, index) => (
                            <div
                                key={index}
                                onClick={() => {
                                    onChange(country)
                                    setIsOpen(false)
                                }}
                                className={`px-4 py-1 rounded-sm cursor-pointer hover:bg-brand-blue-100 hover:text-white`}
                            >
                                {
                                    selected?.name === country.name ? (
                                        <Check className='inline-block mr-1' />
                                    ) : (
                                        <Check className='opacity-0 inline-block mr-1' />
                                    )
                                }
                                <span className='inline-block'>
                                    {country.name} ({country.code})
                                </span>
                            </div>
                        ))}
                    </div>

                    <div
                        onMouseEnter={() => startScrolling('down')}
                        onMouseLeave={stopScrolling}
                        className="flex justify-center items-center h-6 bg-gray-100 hover:bg-gray-200 cursor-pointer"
                    >
                        <ChevronDown size={18} />
                    </div>
                </div>
            )}
        </div>
    )
}
