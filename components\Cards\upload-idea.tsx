"use client";

import { ArrowR<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";
import { ButtonHTMLAttributes } from "react";

interface UploadIdeaProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    /**
     * Main heading text displayed at the top of the card.
     * Supports multiline strings – use "\n" to force a line-break if needed.
     * @default "You get the point you can rent anything!"
     */
    heading?: string;
    /**
     * Supporting subtitle displayed under the heading.
     * @default "Upload your first item and start earning!"
     */
    subheading?: string;
    /**
     * Label shown inside the call-to-action button.
     * @default "Upload Item"
     */
    buttonText?: string;
    /**
     * Additional Tailwind classes to customise the card’s wrapper.
     */
    className?: string;
}

export function UploadIdea({
    heading = "You get the point you can rent anything!",
    subheading = "Upload your first item and start earning!",
    buttonText = "Upload Item",
    className,
    onClick,
    ...buttonProps
}: UploadIdeaProps) {
    return (
        <div
            className={cn(
                "bg-white rounded-lg shadow-lg  p-8", // fixed padding & shadow as per design
                className
            )}
        >
            {/* Heading */}
            <h3 className="text-brand-600 text-[28px] leading-tight font-medium whitespace-pre-line mb-4">
                {heading}
            </h3>

            {/* Sub-heading */}
            <p className="text-brand-300 text-lg mb-8">
                {subheading}
            </p>

            {/* Call-to-action button */}
            <button
                type="button"
                onClick={onClick}
                className="cursor-pointer w-fit flex items-center justify-center gap-2 bg-brand-400 text-brand-600 font-medium rounded-lg py-3 px-6 hover:bg-brand-1000 hover:text-white hover:shadow-customHover"
                {...buttonProps}
            >
                {buttonText}
                <ArrowRight size={18} />
            </button>
        </div>
    );
} 