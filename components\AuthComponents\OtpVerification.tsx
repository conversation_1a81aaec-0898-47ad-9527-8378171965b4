import { useState } from "react"
import { InputOTP, InputOTPGroup, InputOTPSlot } from "../ui/input-otp"

interface OtpVerificationProps {
    isEmailMode: boolean
    email: string
    selectedCountry: { name: string; code: string }
    phone: string
    onVerifySuccess: () => void
    onBack?: () => void
}

export default function OtpVerification({
    isEmailMode,
    email,
    selectedCountry,
    phone,
    onVerifySuccess
}: OtpVerificationProps) {
    const [otpValue, setOtpValue] = useState("")
    const [otpError, setOtpError] = useState(false)

    const isOtpComplete = otpValue.length === 6

    const handleOtpChange = (value: string) => {
        setOtpValue(value)
        // Clear error when user starts typing
        if (otpError) {
            setOtpError(false)
        }
    }

    const handleVerifyOtp = () => {
        if (isOtpComplete) {
            // Static error simulation - you can replace this with actual API call
            if (otpValue === "123456" || otpValue === "123457" || otpValue === "123458" || otpValue === "123459" || otpValue === "123460") {
                // Success case - move to finish signup
                onVerifySuccess()
                console.log("OTP verified successfully:", otpValue)
            } else {
                // Error case
                setOtpError(true)
                setOtpValue("")
                console.log("OTP verification failed")
            }
        }
    }

    const handleResend = () => {
        // Handle resend logic here
        setOtpError(false)
        setOtpValue("")
        console.log("Resending OTP...")
    }

    return (
        <>
            <p className="text-black text-lg px-8 mt-6">
                {isEmailMode
                    ? `Enter the code we sent over to @ ${email}`
                    : `Enter the code we sent over SMS to ${selectedCountry.code} ${phone}`
                }
            </p>
            <div className="px-8 mt-10">
                <div className="flex gap-3 justify-center">
                    <InputOTP
                        maxLength={6}
                        value={otpValue}
                        onChange={handleOtpChange}
                    >
                        <InputOTPGroup className="gap-x-6">
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={0} />
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={1} />
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={2} />
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={3} />
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={4} />
                            <InputOTPSlot className={`border text-black rounded-lg w-[70px] h-[95px] text-5xl font-regular focus:border-brand-blue-200 focus:outline-none focus:ring-2 shadow-none outline-none data-[active=true]:ring-ring/0 ${otpError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-brand-gray-970 focus:border-brand-blue-200 focus:ring-brand-blue-200 data-[active=true]:border-brand-blue-200'}`} index={5} />
                        </InputOTPGroup>
                    </InputOTP>
                </div>
            </div>
            <p className={`${otpError ? "opacity-100 visible" : "opacity-0 invisible"} text-brand-red-600 text-lg px-8 my-10 `}>
                The verification code you entered is incorrect or has expired.
            </p>
            <div className="px-8 py-6 flex justify-between border-t border-brand-gray-900">
                <button
                    onClick={handleResend}
                    className="text-black text-lg font-regular underline text-left p-0 m-0 cursor-pointer hover:text-brand-blue-200">
                    Resend
                </button>
                <button
                    onClick={handleVerifyOtp}
                    disabled={!isOtpComplete}
                    className={`${!isOtpComplete ? "bg-background-disabled" : "bg-background-custom3"} cursor-pointer text-white text-xl font-medium rounded-xl px-20 py-4 disabled:cursor-not-allowed`}>
                    Continue
                </button>
            </div>
        </>
    )
} 