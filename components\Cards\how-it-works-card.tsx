"use client";

import { ElementType } from "react";
import { cn } from "@/lib/utils";

interface HowItWorksCardProps {
    step: number;
    icon: ElementType; // Lucide icon component (or any React component that accepts size/className)
    /** Main heading text, e.g. "Search" */
    title: string;
    /** Supporting description text */
    description: string;
    className?: string;
}

export function HowItWorksCard({
    step,
    icon: Icon,
    title,
    description,
    className,
}: HowItWorksCardProps) {
    return (
        <div
            className={cn(
                "relative flex flex-col items-center text-center bg-white border border-brand-700 rounded-lg pt-20 pb-10 px-6",
                className
            )}
        >
            {/* Step badge */}
            <div className="absolute -top-10 left-1/2 -translate-x-1/2 w-[70px] h-[70px] flex items-center justify-center rounded-sm bg-white border border-brand-700 text-brand-600 font-semibold text-[55px]">
                {step}
            </div>

            {/* Icon */}
            <Icon className="text-brand-1000 mb-6" />

            {/* Title */}
            <h3 className="text-5xl font-semibold text-brand-600 mt-2 capitalize">
                {title}
            </h3>

            {/* Gradient divider */}
            <div
                className="w-full my-8"
                style={{
                    border: "1px solid",
                    borderImageSource:
                        "linear-gradient(90deg, #FDFDFD 0%, #B8B8B8 47.5%, #FDFDFD 100%)",
                    borderImageSlice: 1,
                }}
            />

            {/* Description */}
            <p className="text-brand-200 text-2xl leading-snug ">
                {description}
            </p>
        </div>
    );
} 