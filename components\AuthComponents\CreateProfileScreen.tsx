import { X } from "lucide-react"

interface CreateProfileScreenProps {
    onContinue: () => void
    onClose: () => void
}

export default function CreateProfileScreen({ onContinue, onClose }: CreateProfileScreenProps) {
    return (
        <div className="">
            {/* Header with close button */}
            <div className="flex flex-row items-center justify-between border-b border-brand-gray-900 pb-4 px-8">
                <button onClick={onClose} className="cursor-pointer text-black/50">
                    <X size={20} />
                </button>
                <h2 className="text-black text-xl font-medium">Create your profile</h2>
                <div></div>
            </div>

            {/* Content */}
            <div className="text-center space-y-6 pt-8 px-8 pb-8">
                <div className="space-y-4">
                    <h1 className="text-brand-black-300 text-4xl font-medium">
                        Welcome to <span className="text-brand-blue-200 text-4xl font-extrabold">YouHook</span>
                    </h1>

                    <p className="text-brand-black-300 text-2xl">
                        The marketplace for renting anything.
                    </p>
                </div>

                <div className="pt-8">
                    <button
                        onClick={onContinue}
                        className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium hover:bg-brand-black-400 transition-colors cursor-pointer"
                    >
                        Continue
                    </button>
                </div>
            </div>
        </div>
    )
} 