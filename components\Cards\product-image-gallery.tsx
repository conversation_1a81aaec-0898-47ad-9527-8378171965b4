"use client";

import { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Grid3X3, UsersRound } from "lucide-react";

interface ProductImageGalleryProps {
  images: string[];
  title: string;
  className?: string;
}

export default function ProductImageGallery({
  images,
  title,
  className,
}: ProductImageGalleryProps) {
  const [showAllImages, setShowAllImages] = useState(false);

  const thumbnailImages = images.slice(0, 4); // Show max 4 thumbnails

  return (
    <div
      className={cn(
        "space-y-4 grid grid-cols-1 lg:grid-cols-2 gap-x-2",
        className
      )}
    >
      {/* Main Image */}
      <div>
        <div className="relative w-full h-[420px] rounded-tl-lg rounded-bl-lg">
          <Image
            src={thumbnailImages[0]}
            alt={title}
            fill
            className="object-cover rounded-tl-lg rounded-bl-lg block"
            priority
          />

          {/* Features badges */}
          <div className="absolute top-4 left-4 flex gap-3">
            <div className="flex items-center gap-2 border border-brand-gray-1011 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 font-medium">
              <UsersRound />
              Local Pickup
            </div>
            <div className="flex items-center gap-2 border border-brand-gray-1011 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 font-medium">
              <Image src={"/images/coins.png"} alt="" width={22} height={22} />
              Safety Deposit
            </div>
          </div>

          {/* Show all button */}
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="grid grid-cols-2 gap-x-2 relative">
        <button
          onClick={() => setShowAllImages(true)}
          className="absolute bottom-8 right-4 flex items-center gap-4 border border-brand-gray-1011 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 z-10 cursor-pointer"
        >
          <Grid3X3 size={16} />
          Show all
        </button>
        {thumbnailImages.map((image, index) => (
          <div
            key={index}
            className="w-full h-[205px] relative overflow-hidden"
          >
            <Image
              src={image}
              alt={`${title} - Image ${index + 1}`}
              fill
              className="object-cover"
            />
          </div>
        ))}
      </div>

      {/* All Images Modal - Simple implementation */}
      {showAllImages && (
        <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-medium">All Photos</h3>
              <button
                onClick={() => setShowAllImages(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="p-4 grid grid-cols-2 md:grid-cols-3 gap-4">
              {images.map((image, index) => (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-lg w-full h-[200px]"
                >
                  <Image
                    src={image}
                    alt={`${title} - Image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
