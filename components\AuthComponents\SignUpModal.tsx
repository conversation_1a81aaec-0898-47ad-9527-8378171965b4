import {
    <PERSON>alog,
    <PERSON><PERSON><PERSON>lose,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { X, ChevronLeft } from "lucide-react"
import Link from "next/link"
import SocialLoginButtons from "./SocialLoginButtons"
import CountryCodeDropdown from "./CountryCodeDropdown"
import PhoneNumberInput from "./PhoneNumberInput"
import OtpVerification from "./OtpVerification"
import ProfileForm, { ProfileFormData } from "./ProfileForm"
import LocationForm from "./LocationForm"
import CommunityGuidelinesModal from "./CommunityGuidelinesModal"
import DeclineConfirmationModal from "./DeclineConfirmationModal"
import ProfileCreationModal from "./ProfileCreationModal"
import AlreadySignupPhoneScreen from "./AlreadySignupPhoneScreen"
import AlreadySignupEmailScreen from "./AlreadySignupEmailScreen"
import LoginPasswordScreen from "./LoginPasswordScreen"
import ForgotPasswordScreen from "./ForgotPasswordScreen"
import { useState } from "react"
import { AlertPolygon } from "../Icons"

export default function SignUpModal() {
    const [selectedCountry, setSelectedCountry] = useState({ name: "United States", code: "+1" })
    const [phone, setPhone] = useState("")
    const [email, setEmail] = useState("")
    const [showOtp, setShowOtp] = useState(false)
    const [showFinishSignup, setShowFinishSignup] = useState(false)
    const [showLocation, setShowLocation] = useState(false)
    const [showGuidelines, setShowGuidelines] = useState(false)
    const [showDeclineConfirmation, setShowDeclineConfirmation] = useState(false)
    const [showProfileCreation, setShowProfileCreation] = useState(false)
    const [showProfileCreationModal, setShowProfileCreationModal] = useState(false)
    const [userLocation, setUserLocation] = useState("")
    // const [otpValue] = useState("")
    // const [otpError, setOtpError] = useState(false)
    const [phoneError, setPhoneError] = useState(false)
    const [emailError, setEmailError] = useState(false)
    const [isEmailMode, setIsEmailMode] = useState(false)
    const [showAlreadySignupPhone, setShowAlreadySignupPhone] = useState(false)
    const [showAlreadySignupEmail, setShowAlreadySignupEmail] = useState(false)
    const [showLoginPassword, setShowLoginPassword] = useState(false)
    const [showForgotPassword, setShowForgotPassword] = useState(false)
    const [loginPasswordError, setLoginPasswordError] = useState(false)

    // Dummy data for existing users (no API calls)
    // Phone numbers stored with full format: countryCode + number
    const existingPhoneNumbers = [
        "+1(123) 456-780",   // US number
        "+19876543210",   // US number  
        "+15555555555"    // US number
    ]
    const existingEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    const correctPassword = "password123"

    const handleContinue = () => {
        if (isEmailMode) {
            if (email.trim()) {
                // Email validation
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                if (emailRegex.test(email)) {
                    setEmailError(false)
                    // Check if email already exists
                    if (existingEmails.includes(email.toLowerCase())) {
                        setShowAlreadySignupEmail(true)
                    } else {
                        setShowOtp(true)
                    }
                } else {
                    setEmailError(true)
                }
            }
        } else {
            if (phone.trim()) {
                // Static phone validation - you can replace this with actual validation
                if (phone.length >= 10 && phone.length <= 15) {
                    setPhoneError(false)
                    // Check if phone already exists (combine country code + phone number)
                    const fullPhoneNumber = selectedCountry.code + phone
                    if (existingPhoneNumbers.includes(fullPhoneNumber)) {
                        setShowAlreadySignupPhone(true)
                    } else {
                        setShowOtp(true)
                    }
                } else {
                    setPhoneError(true)
                }
            }
        }
    }

    const handleBack = () => {
        if (showForgotPassword) {
            setShowForgotPassword(false)
            setShowLoginPassword(true)
        } else if (showLoginPassword) {
            setShowLoginPassword(false)
            setShowAlreadySignupEmail(true)
            setLoginPasswordError(false)
        } else if (showAlreadySignupEmail) {
            setShowAlreadySignupEmail(false)
            setPhoneError(false)
            setEmailError(false)
        } else if (showAlreadySignupPhone) {
            setShowAlreadySignupPhone(false)
            setPhoneError(false)
            setEmailError(false)
        } else if (showDeclineConfirmation) {
            setShowDeclineConfirmation(false)
            setShowGuidelines(true)
        } else if (showGuidelines) {
            setShowGuidelines(false)
            setShowLocation(true)
        } else if (showLocation) {
            setShowLocation(false)
            setShowFinishSignup(true)
        } else if (showFinishSignup) {
            setShowFinishSignup(false)
            setShowOtp(true)
        } else {
            setShowOtp(false)
            setPhoneError(false)
            setEmailError(false)
        }
    }

    // OTP change handler - keeping for future use
    // const handleOtpChange = (value: string) => {
    //     setOtpValue(value)
    //     // Clear error when user starts typing
    //     if (otpError) {
    //         setOtpError(false)
    //     }
    // }

    const handlePhoneChange = (value: string) => {
        setPhone(value)
        // Clear phone error when user starts typing
        if (phoneError) {
            setPhoneError(false)
        }
    }

    const handleEmailChange = (value: string) => {
        setEmail(value)
        // Clear email error when user starts typing
        if (emailError) {
            setEmailError(false)
        }
    }

    const handleEmailModeToggle = () => {
        setIsEmailMode(!isEmailMode)
        setPhoneError(false)
        setEmailError(false)
        setPhone("")
        setEmail("")
    }

    const handleOtpVerifySuccess = () => {
        setShowOtp(false)
        setShowFinishSignup(true)
    }

    const handleProfileFormSubmit = (data: ProfileFormData) => {
        console.log("Profile data submitted:", {
            firstName: data.firstName,
            lastName: data.lastName,
            birthDate: `${data.birthMonth} ${data.birthDay}, ${data.birthYear}`,
            email: data.email,
            ...(isEmailMode && data.password && { password: data.password }) // Include password if email signup
        })
        // Move to location step
        setShowFinishSignup(false)
        setShowLocation(true)
    }

    const handleLocationSubmit = (location: string) => {
        console.log("Location submitted:", location)
        setUserLocation(location)
        setShowLocation(false)
        setShowGuidelines(true)
    }

    const handleGuidelinesAgree = () => {
        console.log("Guidelines agreed, opening profile creation modal")
        // Open ProfileCreationModal
        setShowProfileCreationModal(true)
    }

    const handleGuidelinesDecline = () => {
        setShowGuidelines(false)
        setShowDeclineConfirmation(true)
    }

    const handleGoBackToGuidelines = () => {
        setShowDeclineConfirmation(false)
        setShowGuidelines(true)
    }

    const handleCancelSignup = () => {
        // Reset all states and close modal or redirect
        console.log("User cancelled signup")
        // You can add modal close logic here
    }

    const handleProfileCreationComplete = (profileData: { profileImage: string | null }) => {
        console.log("Profile creation completed:", profileData)
        console.log("Signup completed successfully with location:", userLocation)
        setShowProfileCreation(false)
        // Handle final signup completion here - close modal, redirect, etc.
    }

    const handleProfileCreationClose = () => {
        setShowProfileCreation(false)
    }

    // New handlers for login flows
    const handleAlreadySignupPhoneSendCode = () => {
        setShowAlreadySignupPhone(false)
        setShowOtp(true)
    }

    const handleAlreadySignupPhoneUseAnother = () => {
        setShowAlreadySignupPhone(false)
        setPhone("")
        setPhoneError(false)
        setEmailError(false)
    }

    const handleAlreadySignupEmailLogin = () => {
        setShowAlreadySignupEmail(false)
        setShowLoginPassword(true)
    }

    const handleAlreadySignupEmailUseAnother = () => {
        setShowAlreadySignupEmail(false)
        setEmail("")
        setPhoneError(false)
        setEmailError(false)
    }

    const handleLoginPasswordSubmit = (password: string) => {
        if (password === correctPassword) {
            // Successful login - you can redirect or close modal
            console.log("Login successful!")
            // For now, just close the modal
            setShowLoginPassword(false)
            setLoginPasswordError(false)
        } else {
            // Wrong password
            setLoginPasswordError(true)
        }
    }

    const handleForgotPassword = () => {
        setShowLoginPassword(false)
        setShowForgotPassword(true)
        setLoginPasswordError(false)
    }

    const handleSendResetLink = (resetEmail: string) => {
        console.log("Reset link sent to:", resetEmail)
        // For demo, just go back to login
        setShowForgotPassword(false)
        setShowLoginPassword(true)
    }

    return (
        <>
            <Dialog>
                <DialogTrigger className="cursor-pointer rounded-xl h-[60px] text-xl font-medium bg-brand-blue-50 text-white px-4 py-2 w-full block text-center">
                    Sign up or log in
                </DialogTrigger>
                <DialogContent
                    onEscapeKeyDown={(e) => e.preventDefault()}
                    className="signup-modal-content px-0 min-w-[650px] shadow-custom-2 pb-0 overflow-y-auto max-h-[70vh] no-scroll"
                >
                    {
                        !showGuidelines && (
                            <DialogHeader
                                className="flex flex-row items-center justify-between border-b border-brand-gray-900 pb-4 px-8">
                                {showOtp || showFinishSignup || showLocation || showGuidelines || showDeclineConfirmation || showAlreadySignupPhone || showAlreadySignupEmail || showLoginPassword || showForgotPassword ? (
                                    <button onClick={handleBack} className="cursor-pointer text-black/50">
                                        <ChevronLeft size={20} />
                                    </button>
                                ) : (
                                    <DialogClose asChild>
                                        <X className="cursor-pointer text-black/50" />
                                    </DialogClose>
                                )}
                                <DialogTitle className={`${showDeclineConfirmation ? "text-brand-black-300 opacity-0" : "text-black text-xl font-medium text-center"} `}>
                                    {showForgotPassword ? "Forgot password?" : showLoginPassword ? "Log in" : showAlreadySignupPhone || showAlreadySignupEmail ? "Welcome back, Mir" : showLocation ? "Last step!" : showFinishSignup ? "Finish signing up" : showOtp ? (isEmailMode ? "Verify your email address" : "Verify your phone number") : "Log in or Sign up"}
                                </DialogTitle>
                                <div></div>
                            </DialogHeader>
                        )
                    }
                    <div>
                        {showAlreadySignupPhone ? (
                            <AlreadySignupPhoneScreen
                                phone={phone}
                                onSendCode={handleAlreadySignupPhoneSendCode}
                                onUseAnother={handleAlreadySignupPhoneUseAnother}
                            />
                        ) : showAlreadySignupEmail ? (
                            <AlreadySignupEmailScreen
                                email={email}
                                onLogin={handleAlreadySignupEmailLogin}
                                onUseAnother={handleAlreadySignupEmailUseAnother}
                            />
                        ) : showLoginPassword ? (
                            <LoginPasswordScreen
                                onLogin={handleLoginPasswordSubmit}
                                onForgotPassword={handleForgotPassword}
                                hasError={loginPasswordError}
                            />
                        ) : showForgotPassword ? (
                            <ForgotPasswordScreen
                                onSendResetLink={handleSendResetLink}
                            />
                        ) : !showOtp && !showFinishSignup && !showLocation && !showGuidelines && !showDeclineConfirmation ? (
                            <>
                                <h3 className="text-black text-3xl font-medium px-8 mb-4">
                                    Welcome to YouHook!
                                </h3>
                                {!isEmailMode ? (
                                    <div className="px-8 mb-2">
                                        <div className="border border-brand-gray-960 rounded-lg ">
                                            <CountryCodeDropdown selected={selectedCountry} onChange={setSelectedCountry} />
                                            <PhoneNumberInput
                                                countryCode={selectedCountry.code}
                                                phone={phone}
                                                onPhoneChange={handlePhoneChange}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="px-8 mb-4">
                                        <div className={`border rounded-lg ${emailError ? 'border-brand-red-700 bg-brand-red-700/10' : 'border-brand-gray-960'}`}>
                                            <div className="px-3 py-2">
                                                <span className={`text-sm ${emailError ? 'text-brand-red-700' : 'text-brand-gray-970'}`}>
                                                    Email address
                                                </span>
                                                <input
                                                    type="email"
                                                    value={email}
                                                    onChange={(e) => handleEmailChange(e.target.value)}
                                                    placeholder="Enter your email"
                                                    className={`w-full text-black text-xl bg-transparent border-none outline-none `}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {
                                    !isEmailMode && (
                                        <>
                                            <p className="font-regular text-brand-gray-950 text-sm px-8">
                                                We&apos;ll send a text to verify your number. Standard messaging and data rates may apply.
                                                <Link href={"#"} className="text-black font-medium">Terms of Service</Link>
                                            </p>
                                            <div className={`${phoneError ? "opacity-100 visible my-10 h-auto py-6" : "opacity-0 invisible py-10 my-0 h-0"} flex items-center gap-x-8 border border-brand-gray-1001 rounded-lg px-8  text-brand-gray-950 text-lg mx-8`}>
                                                <span><AlertPolygon /></span>
                                                <span>
                                                    We can&apos;t send a code to this phone number. Try using a different one.
                                                </span>
                                            </div>
                                        </>
                                    )
                                }
                                {emailError && (
                                    <p className="text-red-500 text-sm px-8 mb-10">
                                        Enter valid email
                                    </p>
                                )}
                                <div className="px-8">
                                    <button
                                        onClick={handleContinue}
                                        disabled={isEmailMode ? !email.trim() : !phone.trim()}
                                        className="bg-background-custom3 cursor-pointer text-white text-xl font-medium px-4 py-2 rounded-xl w-full py-5 disabled:opacity-50 disabled:cursor-not-allowed">
                                        Continue
                                    </button>
                                </div>
                                <div className="px-8 mb-8 mt-10">
                                    <div className="flex flex-row items-center justify-center gap-2">
                                        <span className="w-full h-[1px] bg-brand-gray-970 mt-1"></span>
                                        <span className="text-black text-xl">
                                            or
                                        </span>
                                        <span className="w-full h-[1px] bg-brand-gray-970 mt-1"></span>
                                    </div>
                                </div>
                                <div className="px-8 pb-8">
                                    <SocialLoginButtons onEmailToggle={handleEmailModeToggle} isEmailMode={isEmailMode} />
                                </div>
                            </>
                        ) : showOtp ? (
                            <OtpVerification
                                isEmailMode={isEmailMode}
                                email={email}
                                selectedCountry={selectedCountry}
                                phone={phone}
                                onVerifySuccess={handleOtpVerifySuccess}
                                onBack={handleBack}
                            />
                        ) : showFinishSignup ? (
                            <ProfileForm
                                onSubmit={handleProfileFormSubmit}
                                onBack={handleBack}
                                isEmailSignup={isEmailMode}
                                prefillEmail={isEmailMode ? email : ""}
                                isEmailEditable={!isEmailMode}
                            />
                        ) : showLocation ? (
                            <LocationForm
                                onSubmit={handleLocationSubmit}
                                onBack={handleBack}
                            />
                        ) : showGuidelines ? (
                            <CommunityGuidelinesModal
                                onAgree={handleGuidelinesAgree}
                                onDecline={handleGuidelinesDecline}
                            />
                        ) : (
                            <DeclineConfirmationModal
                                onGoBack={handleGoBackToGuidelines}
                                onCancelSignup={handleCancelSignup}
                            />
                        )}
                    </div>
                </DialogContent>

                {/* Profile Creation Modal */}
                <ProfileCreationModal
                    isOpen={showProfileCreation}
                    onClose={handleProfileCreationClose}
                    onComplete={handleProfileCreationComplete}
                />
            </Dialog>

            {/* Profile Creation Modal - From Guidelines */}
            <ProfileCreationModal
                isOpen={showProfileCreationModal}
                onClose={() => setShowProfileCreationModal(false)}
                onComplete={(profileData) => {
                    console.log("Profile creation completed:", profileData)
                    setShowProfileCreationModal(false)
                }}
            />
        </>
    )
}