import type { Metada<PERSON> } from "next";
import { madeTommySoft } from "./fonts/made-tommy-soft";
import "./globals.css";

export const metadata: Metadata = {
  title: "YouHook",
  description: "Speaker rental platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={madeTommySoft.variable}>
      <head>
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
      </head>
      <body className="font-made-tommy-soft font-normal antialiased">
        {children}
      </body>
    </html>
  );
}
