"use client"

import { ChevronRight } from "lucide-react"

interface FilterCardProps {
    name: string;
    option: string;
    onClick?: () => void
}

export function FilterCard({ name, option, onClick }: FilterCardProps) {
    return (
        <div onClick={onClick} className="px-8 flex justify-between items-center border-t border-brand-black-150 py-5 cursor-pointer hover:bg-brand-gray-650 transition-colors">
            <div>
                <h3 className="font-medium text-xl text-black mb-2">
                    {name}
                </h3>
                <p className="text-brand-gray-550 ">{option}</p>
            </div>
            <div>
                <ChevronRight className="w-6 h-6" />
            </div>
        </div>

    )
}