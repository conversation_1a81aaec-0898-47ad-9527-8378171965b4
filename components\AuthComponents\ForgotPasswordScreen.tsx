import { useState } from "react"

interface ForgotPasswordScreenProps {
    onSendResetLink: (email: string) => void
}

export default function ForgotPasswordScreen({ onSendResetLink }: ForgotPasswordScreenProps) {
    const [email, setEmail] = useState("")
    const [emailFocused, setEmailFocused] = useState(false)

    const handleSubmit = () => {
        if (email.trim()) {
            onSendResetLink(email)
        }
    }

    return (
        <div className="px-8 pb-8 pt-4">
            <div className="mb-6">
                <p className="text-gray-600 text-lg mb-6">
                    Enter the email address associated with your account, and we&apos;ll email you a link to reset your password.
                </p>

                <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg ${emailFocused
                    ? 'border-brand-black-200 border-2'
                    : 'border border-brand-gray-960'
                    }`}>
                    <label
                        className={`
                            absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                            ${emailFocused || email.length > 0
                                ? 'top-0 text-xs text-brand-gray-970'
                                : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                            }
                        `}
                    >
                        Email
                    </label>
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        onFocus={() => setEmailFocused(true)}
                        onBlur={() => setEmailFocused(false)}
                        className="border-none w-full h-full outline-none text-xl"
                    />
                </div>
            </div>

            <button
                onClick={handleSubmit}
                disabled={!email.trim()}
                className={`w-full h-[60px] text-white rounded-lg text-lg font-medium transition-colors flex items-center justify-center gap-2 ${email.trim()
                    ? 'bg-brand-black-200 cursor-pointer'
                    : 'bg-background-disabled cursor-not-allowed'
                    }`}
            >
                Send reset link
            </button>
        </div>
    )
} 