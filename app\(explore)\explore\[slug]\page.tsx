import ProductImageGallery from "@/components/Cards/product-image-gallery";
import ProductDetails from "@/components/Cards/product-details";
import BookingCard from "@/components/Cards/booking-card";
import { primaryCardData } from "@/data";
import { notFound } from "next/navigation";

interface ExploreDetailPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function ExploreDetailPage({
  params,
}: ExploreDetailPageProps) {
  // Await params in Next.js 15
  const { slug } = await params;

  // Find the product based on the slug
  const product = primaryCardData.find((item) => {
    const itemSlug = item.link?.split("/").pop();
    return itemSlug === slug;
  });

  if (!product) {
    notFound();
  }

  // Enhanced product data with additional details for the detail page
  const productData = {
    id: slug,
    title: product.title,
    location: product.location,
    distance: "About 12 miles away",
    rating: product.rating,
    price: product.price,
    priceUnit: product.priceUnit,
    images: [
      product.image,
      "/images/img-2.png",
      "/images/img-3.png",
      "/images/img-4.png",
    ],
    owner: {
      name: "<PERSON>",
      memberSince: "2008",
      avatar: "/images/daniel.png",
    },
    description: getProductDescription(product.title),
    features: ["Local Pickup", "Safety Deposit"],
    rentalRules: [
      "Must be 21+",
      "Must be cleaned before returned",
      "$400 safety deposit",
    ],
  };

  function getProductDescription(title: string) {
    // Generate descriptions based on product title
    const descriptions: Record<string, string> = {
      "Bounce house for kids":
        "Perfect for birthday parties and events! This colorful bounce house will keep kids entertained for hours. Easy setup and takedown included. Great for backyard parties and celebrations.",
      "Red Lam-Truck (4D Mansory Kit) 2025 - 21+ ONLY":
        "Experience luxury with this stunning red Lamborghini truck. Perfect for special occasions, photo shoots, or making a statement. Must be 21+ with valid driver's license.",
      "Speaker set":
        "Professional-grade speaker system perfect for parties, events, and gatherings. Crystal clear sound quality with easy setup. Includes all necessary cables and equipment.",
      "18 ft Trailer, Heavy duty":
        "Heavy-duty 18ft trailer perfect for moving, hauling equipment, or construction projects. Built to handle heavy loads with reliable performance.",
      "Kx 125 dirtbike":
        "Kawasaki KX125 dirt bike in excellent condition. Perfect for off-road adventures and trail riding. Helmet and safety gear recommended.",
      "Big tent for parties and events 40x10":
        "Large 40x10 party tent perfect for weddings, corporate events, and large gatherings. Weather-resistant and professionally installed.",
      "Kids Bounce house":
        "Colorful and safe bounce house designed specifically for children. Perfect for birthday parties and family gatherings. Includes blower and setup.",
      "De Walt drill":
        "Professional DeWalt drill perfect for DIY projects and construction work. Includes battery, charger, and basic drill bit set.",
    };

    return (
      descriptions[title] ||
      "This sturdy item is perfect for your rental needs. High quality and well-maintained. Great for various projects and occasions."
    );
  }

  return (
    <div className="container inner-page-container mx-auto px-4 py-8">
      <ProductImageGallery
        images={productData.images}
        title={productData.title}
      />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Images and Details */}
        <div className="lg:col-span-2 space-y-8">
          <ProductDetails
            title={productData.title}
            location={productData.location}
            distance={productData.distance}
            rating={productData.rating}
            owner={productData.owner}
            description={productData.description}
            features={productData.features}
            rentalRules={productData.rentalRules}
          />
        </div>
        {/* Right Column - Booking Card (Sticky) */}
        <div className="lg:col-span-1">
          <div className="sticky top-32">
            <BookingCard
              price={productData.price}
              priceUnit={productData.priceUnit}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
