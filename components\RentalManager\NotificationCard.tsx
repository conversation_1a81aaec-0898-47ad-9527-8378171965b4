import { CalendarRange, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { RequestIcon2 } from "../Icons";

interface NotificationsProps {
  title: string;
  status: "pending" | "end" | "claim" | "request";
  date?: string;
  itemImage: string;
  name?: string;
  size?: string;
  link?: string;
  requestCount?: number;
}
export default function NotificationCard({
  title,
  status,
  date,
  itemImage,
  name,
  size,
  link,
  requestCount,
}: NotificationsProps) {
  return (
    <div className="border border-black/20 rounded-lg py-6 pl-6 pr-16">
      <h2
        className={`${
          status === "pending" || status === "end"
            ? "text-brand-red-700"
            : "text-brand-black-50"
        } text-lg font-medium leading-6`}
      >
        {status === "pending" ? (
          <span className="text-brand-red-700">
            Don&apos;t forget to activate your rental
          </span>
        ) : status === "end" ? (
          <span className="text-brand-red-700">
            Don&apos;t forget to end your rental
          </span>
        ) : status === "claim" ? (
          <span className="text-brand-black-50">
            Your open claim was sent and is being reviewed
          </span>
        ) : status === "request" ? (
          <span className="text-brand-black-50">
            You received {requestCount} new rental requests
          </span>
        ) : (
          <span className="text-brand-black-50">
            Finish setting up your calendar
          </span>
        )}
      </h2>
      {date && <p className="text-brand-red-700 py-2">{date}</p>}
      {status === "claim" && (
        <>
          <div className="h-10"></div>
        </>
      )}
      {name && size && (
        <div className="flex gap-x-2">
          <Image
            src={itemImage}
            alt={title}
            width={21}
            height={21}
            className="w-[21px] h-[21px] rounded-full "
          />
          <div className="text-sm text-brand-gray-1013">
            {name} | {size}
          </div>
        </div>
      )}
      {status === "request" && (
        <>
          <p className="h-7"></p>
          <div className="py-1 flex items-center gap-x-2 ">
            <span className="w-[23px] h-[23px] rounded-full flex justify-center items-center bg-brand-yellow-100">
              <RequestIcon2 />
            </span>
            <span className="text-brand-gray-1013 text-sm">Request</span>
          </div>
        </>
      )}
      {!status && (
        <>
          <p className="h-7"></p>
          <div className="py-1 flex items-center gap-x-2 ">
            <span className="w-[23px] h-[23px] rounded-full flex justify-center items-center bg-brand-gray-1014">
              <CalendarRange className="text-brand-gray-550" size={14} />
            </span>
            <span className="text-brand-gray-1013 text-sm">Calendar</span>
          </div>
        </>
      )}

      <Link href={link} className="text-black inline-flex mt-3">
        {status === "request" ? (
          <span className="text-brand-black-50">View</span>
        ) : status === "claim" ? (
          <span className="text-brand-black-50">View status</span>
        ) : status === "pending" ? (
          <span className="text-brand-black-50">Activate rental</span>
        ) : status === "end" ? (
          <span className="text-brand-black-50">End rental</span>
        ) : (
          <span className="text-brand-black-50">View</span>
        )}
        <ChevronRight />
      </Link>
    </div>
  );
}
