import { Lock } from "lucide-react"

interface SelfieVerificationScreenProps {
    onContinue: () => void
    onBack?: () => void
}

export default function SelfieVerificationScreen({ onContinue }: SelfieVerificationScreenProps) {
    return (
        <div className="px-8 pb-8">
            <div className="pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-6">
                    Next, take a photo of yourself
                </h2>

                <p className="text-brand-black-300 text-base mb-6">
                    We will compare this photo with your ID photo to make sure they match.
                </p>

                <p className="text-brand-black-300 text-base mb-20">
                    This photo will not be shared anywhere.
                </p>

                {/* Privacy Section */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8">
                    <h4 className="font-medium text-brand-black-300 mb-2">Your Privacy</h4>
                    <p className="text-gray-600 text-sm">
                        We take your privacy seriously. Your ID and data will be kept safe, secure, and will never be shared with anyone.{" "}
                        <a href="#" className="text-brand-blue-200 underline">
                            Learn more
                        </a>
                    </p>
                </div>

                {/* Continue Button */}
                <div className="flex justify-center">
                    <button
                        onClick={onContinue}
                        className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium cursor-pointer hover:bg-brand-black-300 transition-colors flex items-center justify-center gap-2"
                    >
                        <Lock size={20} />
                        Continue
                    </button>
                </div>
            </div>
        </div>
    )
} 