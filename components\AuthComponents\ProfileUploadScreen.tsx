/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useRef, useCallback, useEffect } from "react"
import { CropIcon, ProfileIcon, UploadCloudIcon } from "../Icons"
import Image from "next/image"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../Buttons/skip-button"
import <PERSON><PERSON><PERSON><PERSON> from "./ImageCropper"

interface ProfileUploadScreenProps {
    onImageUpload: (imageData: string) => void
    onSkip: () => void
    onBack?: () => void
    currentScreen?: ScreenState
    uploadedImage?: string | null
    bioText?: string
    onGenerateBio?: () => void
    onBioComplete?: () => void
    onBioTextChange?: (text: string) => void
    onBioDone?: () => void
}

type ScreenState = "upload" | "bio" | "bio-generated"

export default function ProfileUploadScreen({
    onImageUpload,
    onSkip,
    currentScreen: propCurrentScreen = "upload",
    uploadedImage: propUploadedImage = null,
    bioText: propBioText = "",
    onGenerateBio,
    onBioComplete,
    onBioTextChange,
    onBioDone
}: ProfileUploadScreenProps) {
    // onSkip is kept for interface compatibility but currently unused
    void onSkip
    const [uploadedImage, setUploadedImage] = useState<string | null>(propUploadedImage)
    const [showCropOptions, setShowCropOptions] = useState(false)
    const [internalCurrentScreen, setInternalCurrentScreen] = useState<ScreenState>(propCurrentScreen)
    const [internalBioText, setInternalBioText] = useState(propBioText || "")
    const [isCropMode, setIsCropMode] = useState(false)
    const [cropData, setCropData] = useState({ x: 0, y: 0, width: 250, height: 250 })
    const [isDragging, setIsDragging] = useState(false)
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
    const [imageScale, setImageScale] = useState(1)
    const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 })
    const [rotation, setRotation] = useState(0)
    const [isDraggingImage, setIsDraggingImage] = useState(false)
    const [imageDragStart, setImageDragStart] = useState({ x: 0, y: 0 })
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const imageRef = useRef<HTMLImageElement>(null)
    const cropContainerRef = useRef<HTMLDivElement>(null)

    // Use props when available, fallback to internal state
    const currentScreen = propCurrentScreen || internalCurrentScreen
    const bioText = propBioText !== undefined ? propBioText : internalBioText

    const handleUploadClick = (e: React.MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()

        // Use temp input method since it works reliably across all browsers
        createTempFileInput()
    }

    // This function is used by the temp file input in createTempFileInput
    // Keeping it for consistency even though it's not directly used in JSX

    const handleContinue = () => {
        if (uploadedImage) {
            // Pass image data when moving to bio screen
            onImageUpload(uploadedImage)
        }
    }

    // const handleSkipToBio = () => {
    //     // Skip photo upload and go to bio screen
    //     onSkip()
    // }

    const handleChangePhoto = () => {
        setUploadedImage(null)
        setShowCropOptions(false)
        createTempFileInput()
    }

    const handleCropRotate = () => {
        setIsCropMode(true)
        setShowCropOptions(false)
    }

    const handleCropDone = useCallback(() => {
        if (!canvasRef.current || !imageRef.current) return

        const canvas = canvasRef.current
        const ctx = canvas.getContext('2d')
        if (!ctx) return

        // Set canvas size to crop area (square)
        const cropSize = 250
        canvas.width = cropSize
        canvas.height = cropSize

        // Calculate the actual image dimensions and position
        const img = imageRef.current
        const containerSize = 400

        // Save context for rotation
        ctx.save()

        // Translate to center of canvas for rotation
        ctx.translate(cropSize / 2, cropSize / 2)
        ctx.rotate((rotation * Math.PI) / 180)

        // Calculate scaled image dimensions
        const scaledWidth = img.naturalWidth * imageScale
        const scaledHeight = img.naturalHeight * imageScale

        // Calculate the position relative to the crop area
        const imgX = imagePosition.x - cropData.x
        const imgY = imagePosition.y - cropData.y

        // Draw the image with scale and rotation
        ctx.drawImage(
            img,
            -scaledWidth / 2 + imgX - cropSize / 2 + scaledWidth / 2,
            -scaledHeight / 2 + imgY - cropSize / 2 + scaledHeight / 2,
            scaledWidth,
            scaledHeight
        )

        ctx.restore()

        // Get the cropped image as data URL
        const croppedImage = canvas.toDataURL('image/jpeg', 0.9)
        setUploadedImage(croppedImage)
        setIsCropMode(false)
        setShowCropOptions(true)

        // Reset transform states
        setImageScale(1)
        setImagePosition({ x: 0, y: 0 })
        setRotation(0)
    }, [cropData, imageScale, imagePosition, rotation])

    const handleCroppedImage = useCallback((img: string) => {
        setUploadedImage(img)
        setIsCropMode(false)
        setShowCropOptions(true)
    }, [])

    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        setIsDragging(true)
        setDragStart({ x: x - cropData.x, y: y - cropData.y })
    }, [cropData])

    const handleMouseMove = useCallback((e: React.MouseEvent) => {
        if (!isDragging) return

        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left - dragStart.x
        const y = e.clientY - rect.top - dragStart.y

        // Ensure crop area stays within image bounds
        const maxX = 400 - cropData.width
        const maxY = 400 - cropData.height

        setCropData(prev => ({
            ...prev,
            x: Math.max(0, Math.min(x, maxX)),
            y: Math.max(0, Math.min(y, maxY))
        }))
    }, [isDragging, dragStart, cropData.width, cropData.height])

    const handleMouseUp = useCallback(() => {
        setIsDragging(false)
        setIsDraggingImage(false)
    }, [])

    // Image dragging for repositioning
    const handleImageMouseDown = useCallback((e: React.MouseEvent) => {
        e.stopPropagation()
        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        setIsDraggingImage(true)
        setImageDragStart({ x: x - imagePosition.x, y: y - imagePosition.y })
    }, [imagePosition])

    const handleImageMouseMove = useCallback((e: React.MouseEvent) => {
        if (!isDraggingImage) return

        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left - imageDragStart.x
        const y = e.clientY - rect.top - imageDragStart.y

        setImagePosition({ x, y })
    }, [isDraggingImage, imageDragStart])

    // Zoom functionality
    const handleZoomIn = useCallback(() => {
        setImageScale(prev => Math.min(prev + 0.2, 3))
    }, [])

    const handleZoomOut = useCallback(() => {
        setImageScale(prev => Math.max(prev - 0.2, 0.5))
    }, [])

    // Rotate functionality
    const handleRotateLeft = useCallback(() => {
        setRotation(prev => prev - 90)
    }, [])

    const handleRotateRight = useCallback(() => {
        setRotation(prev => prev + 90)
    }, [])

    // Mouse wheel zoom
    const handleWheel = useCallback((e: React.WheelEvent) => {
        e.preventDefault()
        const delta = e.deltaY > 0 ? -0.1 : 0.1
        setImageScale(prev => Math.max(0.5, Math.min(3, prev + delta)))
    }, [])

    // Initialize crop area when entering crop mode
    useEffect(() => {
        if (isCropMode && uploadedImage) {
            // Center the crop area
            const containerSize = 400
            const cropSize = 250
            const centerX = (containerSize - cropSize) / 2
            const centerY = (containerSize - cropSize) / 2

            setCropData({
                x: centerX,
                y: centerY,
                width: cropSize,
                height: cropSize
            })

            // Reset image transforms
            setImageScale(1)
            setImagePosition({ x: 0, y: 0 })
            setRotation(0)
        }
    }, [isCropMode, uploadedImage])

    const handleGenerateBio = () => {
        if (onGenerateBio) {
            onGenerateBio()
        } else {
            // Fallback for internal state management
            const staticBio = "Passionate about connecting with like-minded individuals and exploring new opportunities. I believe in building meaningful relationships and making a positive impact in my community."
            setInternalBioText(staticBio)
            setInternalCurrentScreen("bio-generated")
        }
    }

    const handleBioComplete = () => {
        if (onBioComplete) {
            onBioComplete()
        } else {
            // Fallback for internal state management
            onImageUpload(uploadedImage || "")
        }
    }

    // Unused but kept for potential future use
    // const handleBackFromBio = () => {
    //     if (currentScreen === "bio-generated") {
    //         setInternalCurrentScreen("bio")
    //     } else {
    //         setInternalCurrentScreen("upload")
    //     }
    // }

    const createTempFileInput = () => {
        const tempInput = document.createElement('input')
        tempInput.type = 'file'
        tempInput.accept = 'image/*'
        tempInput.style.display = 'none'

        tempInput.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0]
            if (file) {
                const reader = new FileReader()
                reader.onload = (e) => {
                    const result = e.target?.result as string
                    setUploadedImage(result)
                    setShowCropOptions(true)
                }
                reader.readAsDataURL(file)
            }
            // Clean up
            if (tempInput.parentNode) {
                tempInput.parentNode.removeChild(tempInput)
            }
        }

        document.body.appendChild(tempInput)
        tempInput.click()
    }

    // Bio creation screen
    const renderBioScreen = () => (
        <div className="px-8 pb-8">
            <div className="text-center pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-10">
                    Add a bio
                </h2>

                <div className="mb-6">
                    <div className="relative">
                        <textarea
                            value={bioText}
                            onChange={(e) => {
                                const newText = e.target.value
                                if (onBioTextChange) {
                                    onBioTextChange(newText)
                                } else {
                                    setInternalBioText(newText)
                                }
                            }}
                            placeholder="Add you bio! (A good bio helps build trust with other users)"
                            className="w-full h-[300px] rounded-xl p-5 resize-none outline-none border border-brand-gray-960 text-sm"
                            maxLength={500}
                        />
                        <div className="flex justify-end mt-2 absolute bottom-5 right-5">
                            <button
                                onClick={handleGenerateBio}
                                className="bg-background-custom3 h-[40px] text-white w-[130px] rounded-full flex items-center gap-2 text-sm cursor-pointer justify-center hover:w-[200px] transition-all duration-300 group"
                            >
                                <span>
                                    <Image src={"/images/generate-bio-icon.png"} alt="Generate" width={18} height={18} />
                                </span>
                                <span>
                                    Generate
                                </span>
                                <span className="hidden group-hover:inline-block transition-opacity duration-300 whitespace-nowrap">
                                    bio with AI
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <div className="space-y-3">
                    <button
                        onClick={() => {
                            if (bioText.trim()) {
                                handleBioComplete()
                            }
                        }}
                        disabled={!bioText.trim()}
                        className={`w-full h-[60px] text-white rounded-lg text-lg font-medium transition-colors ${bioText.trim()
                            ? 'bg-brand-black-200  cursor-pointer'
                            : 'bg-background-disabled  cursor-not-allowed'
                            }`}
                    >
                        Continue
                    </button>

                    <SkipButton onClick={handleBioComplete} />
                </div>
            </div>
        </div>
    )

    // Bio screen with generated content
    const renderBioGeneratedScreen = () => (
        <div className="px-8 pb-8">
            <div className="text-center pt-6">
                <h2 className="text-brand-black-300 text-2xl font-medium mb-10">
                    To start add a brief description
                </h2>

                <div className="gradient-border-wrapper  mb-10 rounded-lg ">
                    <textarea
                        value={bioText}
                        onChange={(e) => {
                            const newText = e.target.value
                            if (onBioTextChange) {
                                onBioTextChange(newText)
                            } else {
                                setInternalBioText(newText)
                            }
                        }}
                        className="textarea-inside p-5 rounded-lg bg-white text-sm resize-none outline-none border-none h-[300px] w-full block"
                        maxLength={500}
                    />
                </div>

                <div className="space-y-3">
                    <button
                        onClick={handleGenerateBio}
                        className="w-full h-[60px] bg-background-custom3 text-white rounded-lg text-lg font-medium cursor-pointer flex items-center justify-center gap-2"
                    >
                        <span>
                            <Image src={"/images/generate-bio-icon.png"} alt="Generate" width={18} height={18} />
                        </span>
                        <span>
                            Generate bio
                        </span>
                    </button>

                    <button
                        onClick={() => {
                            if (onBioDone) {
                                onBioDone()
                            } else {
                                // Fallback for internal state management
                                setInternalCurrentScreen("bio")
                            }
                        }}
                        className="w-full h-[60px] border border-brand-black-200 text-brand-black-200 rounded-lg text-lg font-medium cursor-pointer"
                    >
                        Done
                    </button>
                </div>
            </div>
        </div>
    )

    // Render different screens based on current state
    if (isCropMode) {
        return (
            <ImageCropper
                imageSrc={uploadedImage || ""}
                onCropComplete={handleCroppedImage}
            />
        )
    }

    if (currentScreen === "bio") {
        return renderBioScreen()
    }

    if (currentScreen === "bio-generated") {
        return renderBioGeneratedScreen()
    }

    // Default upload screen
    return (
        <div className="px-8 pb-8">
            <div className="text-center pt-6">
                {!showCropOptions && (
                    <h2 className="text-brand-black-300 text-2xl font-medium mb-10">
                        {uploadedImage ? "Looking good!" : "Add your profile photo"}
                    </h2>
                )}

                {/* Profile Image Preview or Placeholder */}
                <div className={`${showCropOptions ? "mb-4" : "mb-20"} flex justify-center`}>
                    {uploadedImage ? (
                        <div className="w-[250px] h-[250px] rounded-full overflow-hidden border-4 border-gray-200">
                            {/* eslint-disable-next-line @next/next/no-img-element */}
                            <img
                                src={uploadedImage}
                                alt="Profile preview"
                                className="w-full h-full object-cover"
                            />
                        </div>
                    ) : (
                        <div className="flex items-center justify-center">
                            <ProfileIcon />
                        </div>
                    )}
                </div>

                {/* Crop or Rotate button - only shown after image upload */}
                {showCropOptions && (
                    <div className="flex justify-center mb-10">
                        <button
                            onClick={handleCropRotate}
                            className="flex items-center gap-2 text-brand-black-300 text-lg font-medium hover:text-brand-black-400 transition-colors cursor-pointer hover:bg-brand-1500/50 rounded-lg px-6 py-4"
                        >
                            <CropIcon />
                            Crop & Rotate
                        </button>
                    </div>
                )}

                <div className="space-y-4">
                    {uploadedImage ? (
                        <button
                            onClick={handleContinue}
                            className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium flex items-center justify-center gap-2 cursor-pointer"
                        >
                            Continue
                        </button>
                    ) : (
                        <button
                            type="button"
                            onClick={handleUploadClick}
                            className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium flex items-center justify-center gap-2 cursor-pointer"
                        >
                            Upload photo
                            <UploadCloudIcon />
                        </button>
                    )}
                    {
                        uploadedImage ? (
                            <button
                                onClick={handleChangePhoto}
                                className="text-brand-black-200 border border-brand-black-200 rounded-lg w-full h-[60px] text-lg font-medium  cursor-pointer"
                            >
                                Change photo
                            </button>
                        ) : (
                            <SkipButton onClick={handleBioComplete} />
                        )
                    }
                </div>
            </div>
        </div>
    )
} 