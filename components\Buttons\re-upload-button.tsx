interface ReUploadButtonProps {
    onClick: () => void
    text: string
    className?: string
}

export default function ReUploadButton({ onClick, text, className = "" }: ReUploadButtonProps) {
    return (
        <button
            onClick={onClick}
            className={`text-brand-black-200 px-4 py-2 rounded-md  underline cursor-pointer hover:bg-brand-gray-660 ${className}`}
        >
            {text}
        </button>
    )
} 