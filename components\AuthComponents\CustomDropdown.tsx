import { Check } from 'lucide-react'
import { useState, useRef, useEffect } from 'react'

const ChevronDownIcon = ({ className = '' }) => (
    <svg className={className} width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path d="M4 6L8 10L12 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
)

interface CustomDropdownProps {
    value: string
    onChange: (value: string) => void
    options: { value: string; label: string }[]
    placeholder: string
    className?: string
    text?: string
    error?: boolean
}

export default function CustomDropdown({ value, onChange, options, placeholder, className = '', error = false, text = '' }: CustomDropdownProps) {
    const [isOpen, setIsOpen] = useState(false)
    const dropdownRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false)
            }
        }

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside)
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [isOpen])

    const handleToggle = (e: React.MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setIsOpen(!isOpen)
    }

    const handleSelect = (selectedValue: string) => {
        onChange(selectedValue)
        setIsOpen(false)
    }

    const selectedOption = options.find(opt => opt.value === value)

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <button
                type="button"
                onClick={handleToggle}
                className={`w-full border rounded-sm px-3 py-4 text-left flex items-center justify-between bg-white outline-none cursor-pointer ${error ? 'border-red-500' : 'border-brand-gray-960'
                    } ${value ? 'text-brand-gray-1002' : 'text-brand-gray-1002'}`}
            >
                <span>{selectedOption ? selectedOption.label : placeholder}</span>
                <ChevronDownIcon className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`} />
            </button>

            {isOpen && (
                <div className="absolute top-full left-0 right-0 z-[9999] bg-[#5f5f5f] border border-brand-gray-960 rounded-sm mt-1 w-[132px] max-h-60 no-scroll overflow-y-auto shadow-lg">
                    <div className="flex items-center gap-2 p-2 text-white/20">
                        <Check className="w-4 h-4" />
                        <span className="">{text}</span>
                    </div>
                    {options.map((option) => (
                        <div
                            key={option.value}
                            onClick={() => handleSelect(option.value)}
                            className=" hover:bg-brand-blue-200 p-1 cursor-pointer text-white flex items-center gap-2"
                        >
                            <Check className="w-4 h-4 opacity-0" />
                            {option.label}
                        </div>
                    ))}
                </div>
            )}
        </div>
    )
} 