import { DropdownMenu, DropdownMenuItem, DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Menu } from "lucide-react";
import { DropdownMenuContent } from "@radix-ui/react-dropdown-menu";
import { DropdownMenuSeparator } from "@radix-ui/react-dropdown-menu";
import SignUpModal from "../AuthComponents/SignUpModal";
import { useState } from "react";
import { HeaderDropdownLink } from "../Buttons";
import { UserIcon } from "../Icons";

export default function HeaderDropdown({ isInnerpages = false }: { isInnerpages?: boolean }) {
    const [isLoggedIn, setIsLoggedIn] = useState(true);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger className={`${isInnerpages ? "border-2 border-brand-gray-850" : "border-2 border-brand-gray-350"} cursor-pointer  rounded-full flex items-center pl-4 pr-2 py-2`}>
                <Menu className={`${isInnerpages ? "text-brand-gray-1010" : "text-brand-gray-350"} w-6 h-6 mr-4`} />
                <UserIcon />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[340px] bg-white rounded-xl p-0 py-4 shadow-custom-7 mt-3">
                {
                    !isLoggedIn && (
                        <>
                            <DropdownMenuItem onClick={(e) => e.preventDefault()} className="px-6">
                                <SignUpModal />
                            </DropdownMenuItem>
                            <HeaderDropdownLink href={"#"} text="Create listing" />
                            <HeaderDropdownLink href={"#"} text="What is YouHook?" />
                            <DropdownMenuSeparator />
                            <HeaderDropdownLink href={"#"} text="Contact us" />
                            <HeaderDropdownLink href={"#"} text="Help center" />
                        </>
                    )
                }
                {
                    isLoggedIn && (
                        <>
                            <HeaderDropdownLink href={"#"} text="Messages" count={9} />
                            <HeaderDropdownLink href={"#"} text="Favorites" />
                            <DropdownMenuSeparator />
                            <HeaderDropdownLink href={"#"} text="Create Listing" />
                            <HeaderDropdownLink href={"#"} text="My Listings" />
                            <HeaderDropdownLink href={"/rental-manager-owner"} text="Rental Manager" count={2} />
                            <HeaderDropdownLink href={"#"} text="Account" />
                            <DropdownMenuSeparator />
                            <HeaderDropdownLink href={"#"} text="Download YouHook" />
                            <HeaderDropdownLink href={"#"} text="Help" />
                            <HeaderDropdownLink href={"#"} text="Log out" onClick={() => setIsLoggedIn(false)} />
                        </>
                    )
                }
            </DropdownMenuContent>
        </DropdownMenu>
    )
}