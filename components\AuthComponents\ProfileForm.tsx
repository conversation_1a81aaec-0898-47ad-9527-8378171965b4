import { useState } from "react"
import Link from "next/link"
import CustomDropdown from "./CustomDropdown"

interface ProfileFormProps {
    onSubmit: (data: ProfileFormData) => void
    onBack?: () => void
    isEmailSignup?: boolean  // New prop to determine if password fields should be shown
    prefillEmail?: string    // New prop to pre-fill email
    isEmailEditable?: boolean // New prop to control if email can be edited
}

export interface ProfileFormData {
    firstName: string
    lastName: string
    birthMonth: string
    birthDay: string
    birthYear: string
    email: string
    password?: string        // Optional since phone signup won't have password
}

export default function ProfileForm({
    onSubmit,
    isEmailSignup = false,
    prefillEmail = "",
    isEmailEditable = true
}: ProfileFormProps) {
    const [firstName, setFirstName] = useState("")
    const [lastName, setLastName] = useState("")
    const [birthMonth, setBirthMonth] = useState("")
    const [birthDay, setBirthDay] = useState("")
    const [birthYear, setBirthYear] = useState("")
    const [signupEmail, setSignupEmail] = useState(prefillEmail) // Use prefillEmail
    const [formErrors, setFormErrors] = useState({
        firstName: false,
        lastName: false,
        birthDate: false,
        email: false,
        password: false,
        confirmPassword: false,
        passwordMismatch: false,
        ageRestriction: false
    })

    // Focus states for floating labels
    const [firstNameFocused, setFirstNameFocused] = useState(false)
    const [lastNameFocused, setLastNameFocused] = useState(false)
    const [emailFocused, setEmailFocused] = useState(false)
    const [passwordFocused, setPasswordFocused] = useState(false)
    const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false)

    // Password states - only needed for email signup
    const [password, setPassword] = useState("")
    const [confirmPassword, setConfirmPassword] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)

    // Generate month options
    const months = [
        "Jan", "Feb", "Mar", "Apr", "May", "Jun",
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ]

    // Generate day options (1-31)
    const days = Array.from({ length: 31 }, (_, i) => i + 1)

    // Generate year options (from 100 years ago to current year)
    const currentYear = new Date().getFullYear()
    const years = Array.from({ length: 100 }, (_, i) => currentYear - i)

    // Prepare options for custom dropdowns
    const monthOptions = months.map(month => ({ value: month, label: month }))
    const dayOptions = days.map(day => ({ value: day.toString(), label: day.toString() }))
    const yearOptions = years.map(year => ({ value: year.toString(), label: year.toString() }))

    const calculateAge = (birthMonth: string, birthDay: string, birthYear: string) => {
        if (!birthMonth || !birthDay || !birthYear) return null

        const today = new Date()
        const birthDate = new Date(parseInt(birthYear), months.indexOf(birthMonth), parseInt(birthDay))
        let age = today.getFullYear() - birthDate.getFullYear()
        const monthDiff = today.getMonth() - birthDate.getMonth()

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--
        }

        return age
    }

    // Password validation functions
    const isPasswordValid = () => {
        return password.length >= 8 &&
            /\d/.test(password) &&
            /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }

    const getPasswordStrength = () => {
        if (password.length === 0) return null
        if (password.length < 8) return 'weak'
        if (!(/\d/.test(password) && /[!@#$%^&*(),.?":{}|<>]/.test(password))) return 'weak'
        return 'strong'
    }

    const hasMinLength = () => password.length >= 8
    const hasNumberOrSymbol = () => /\d/.test(password) || /[!@#$%^&*(),.?":{}|<>]/.test(password)

    const handleFinishSignup = () => {
        const errors = {
            firstName: !firstName.trim(),
            lastName: !lastName.trim(),
            birthDate: !birthMonth || !birthDay || !birthYear,
            email: !signupEmail.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(signupEmail),
            password: isEmailSignup ? (!password.trim() || !isPasswordValid()) : false, // Only validate password for email signup
            confirmPassword: isEmailSignup ? !confirmPassword.trim() : false, // Only validate confirm password for email signup
            passwordMismatch: isEmailSignup ? (password !== confirmPassword) : false, // Only check password match for email signup
            ageRestriction: false
        }

        // Check age restriction
        const age = calculateAge(birthMonth, birthDay, birthYear)
        if (age !== null && age < 18) {
            errors.ageRestriction = true
        }

        setFormErrors(errors)

        // If no errors, proceed with signup
        if (!Object.values(errors).some(error => error)) {
            const formData: ProfileFormData = {
                firstName,
                lastName,
                birthMonth,
                birthDay,
                birthYear,
                email: signupEmail,
                ...(isEmailSignup && { password }) // Only include password for email signup
            }
            onSubmit(formData)
        }
    }

    const clearFieldError = (field: keyof typeof formErrors) => {
        if (formErrors[field]) {
            setFormErrors(prev => ({ ...prev, [field]: false, ageRestriction: false }))
        }
    }

    return (
        <div className="px-8 pb-8 pt-4">
            {/* Name Section */}
            <div className="mb-8">
                <h3 className="text-brand-black-300 text-lg font-medium mb-3">Name</h3>
                <div>
                    {/* First Name Input */}
                    <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg rounded-b-none ${firstNameFocused
                        ? formErrors.firstName
                            ? 'border-red-500 border-2 border-b'
                            : 'border-brand-black-200 border-2 border-b'
                        : formErrors.firstName
                            ? 'border-red-500 border-t border-l border-r border-b'
                            : 'border-t border-l border-r border-b border-brand-gray-960'
                        }`}>
                        <label
                            className={`
                                absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                                ${firstNameFocused || firstName.length > 0
                                    ? formErrors.firstName
                                        ? 'top-0 text-xs text-red-500'
                                        : 'top-0 text-xs text-brand-gray-970'
                                    : formErrors.firstName
                                        ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                        : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                                }
                            `}
                        >
                            First name
                        </label>
                        <input
                            type="text"
                            value={firstName}
                            onChange={(e) => {
                                setFirstName(e.target.value)
                                clearFieldError('firstName')
                            }}
                            onFocus={() => setFirstNameFocused(true)}
                            onBlur={() => setFirstNameFocused(false)}
                            className="border-none w-full h-full outline-none text-xl"
                        />
                    </div>

                    {/* Last Name Input */}
                    <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg rounded-t-none ${lastNameFocused
                        ? formErrors.lastName
                            ? 'border-red-500 border-2'
                            : 'border-brand-black-200 border-2'
                        : formErrors.lastName
                            ? 'border-red-500 border'
                            : 'border border-brand-gray-960'
                        }`}>
                        <label
                            className={`
                                absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                                ${lastNameFocused || lastName.length > 0
                                    ? formErrors.lastName
                                        ? 'top-0 text-xs text-red-500'
                                        : 'top-0 text-xs text-brand-gray-970'
                                    : formErrors.lastName
                                        ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                        : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                                }
                            `}
                        >
                            Last name
                        </label>
                        <input
                            type="text"
                            value={lastName}
                            onChange={(e) => {
                                setLastName(e.target.value)
                                clearFieldError('lastName')
                            }}
                            onFocus={() => setLastNameFocused(true)}
                            onBlur={() => setLastNameFocused(false)}
                            className="border-none w-full h-full outline-none text-xl"
                        />
                    </div>
                    {(formErrors.firstName || formErrors.lastName) && (
                        <p className="text-red-500 text-sm mt-2">First and last name required</p>
                    )}
                </div>
            </div>

            {/* Date of Birth Section */}
            <div className="mb-6">
                <h3 className="text-brand-black-300 text-lg font-medium mb-4">Date of birth</h3>
                <div className="flex items-center gap-1 mb-3">
                    <span className="text-brand-gray-1002 font-medium">Birthday</span>
                    <div className="w-4 h-4 rounded-full border border-brand-gray-1002 flex items-center justify-center">
                        <span className="text-brand-gray-1002 text-xs">?</span>
                    </div>
                </div>
                <div className="flex gap-3 mb-2">
                    <CustomDropdown
                        value={birthMonth}
                        onChange={(value) => {
                            setBirthMonth(value)
                            clearFieldError('birthDate')
                        }}
                        options={monthOptions}
                        placeholder="Month"
                        className="flex-1"
                        error={!!(formErrors.birthDate || formErrors.ageRestriction)}
                        text="Month"
                    />
                    <CustomDropdown
                        value={birthDay}
                        onChange={(value) => {
                            setBirthDay(value)
                            clearFieldError('birthDate')
                        }}
                        options={dayOptions}
                        placeholder="Day"
                        className="flex-1"
                        error={!!(formErrors.birthDate || formErrors.ageRestriction)}
                        text="Day"
                    />
                    <CustomDropdown
                        value={birthYear}
                        onChange={(value) => {
                            setBirthYear(value)
                            clearFieldError('birthDate')
                        }}
                        options={yearOptions}
                        placeholder="Year"
                        className="flex-1"
                        error={!!(formErrors.birthDate || formErrors.ageRestriction)}
                        text="Year"
                    />
                </div>
                {formErrors.birthDate && (
                    <p className="text-red-500 text-sm mb-2">Select your birthday</p>
                )}
                {formErrors.ageRestriction && (
                    <p className="text-red-500 text-sm mb-2">
                        You must be 18 or older to use YouHook. Other people won&apos;t see your birthday.
                    </p>
                )}
            </div>

            {/* Contact Info Section */}
            <div className="mb-8">
                <h3 className="text-brand-black-300 text-lg font-medium mb-4">Contact info</h3>
                <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg ${emailFocused
                    ? formErrors.email
                        ? 'border-red-500 border-2'
                        : 'border-brand-black-200 border-2'
                    : formErrors.email
                        ? 'border-red-500 border'
                        : 'border border-brand-gray-960'
                    }`}>
                    <label
                        className={`
                            absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                            ${emailFocused || signupEmail.length > 0
                                ? formErrors.email
                                    ? 'top-0 text-xs text-red-500'
                                    : 'top-0 text-xs text-brand-gray-970'
                                : formErrors.email
                                    ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                    : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                            }
                        `}
                    >
                        Email
                    </label>
                    <input
                        type="email"
                        value={signupEmail}
                        onChange={(e) => {
                            if (isEmailEditable) {
                                setSignupEmail(e.target.value)
                                clearFieldError('email')
                            }
                        }}
                        onFocus={() => setEmailFocused(true)}
                        onBlur={() => setEmailFocused(false)}
                        className={`border-none w-full h-full outline-none text-xl ${!isEmailEditable ? 'bg-brand-1100 cursor-not-allowed' : ''}`}
                        readOnly={!isEmailEditable}
                    />
                </div>
                <p className="text-brand-gray-950 text-sm mt-2">
                    We&apos;ll email you your receipts and confirmations.
                </p>
                {formErrors.email && (
                    <p className="text-red-500 text-sm mt-1">Email is required</p>
                )}
            </div>

            {/* Password Section - Only shown for email signup */}
            {isEmailSignup && (
                <div className="mb-8">
                    <h3 className="text-brand-black-300 text-lg font-medium mb-4">Password</h3>

                    {/* Password Input */}
                    <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg mb-3 ${passwordFocused
                        ? formErrors.password
                            ? 'border-red-500 border-2'
                            : 'border-brand-black-200 border-2'
                        : formErrors.password
                            ? 'border-red-500 border'
                            : 'border border-brand-gray-960'
                        }`}>
                        <label
                            className={`
                                absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                                ${passwordFocused || password.length > 0
                                    ? formErrors.password
                                        ? 'top-0 text-xs text-red-500'
                                        : 'top-0 text-xs text-brand-gray-970'
                                    : formErrors.password
                                        ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                        : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                                }
                            `}
                        >
                            Password
                        </label>
                        <input
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => {
                                setPassword(e.target.value)
                                clearFieldError('password')
                            }}
                            onFocus={() => setPasswordFocused(true)}
                            onBlur={() => setPasswordFocused(false)}
                            className="border-none w-full h-full outline-none text-xl pr-16"
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 text-brand-black-300 underline text-sm font-normal"
                        >
                            {showPassword ? 'Hide' : 'Show'}
                        </button>
                    </div>

                    {/* Password Strength Indicators */}
                    {password.length > 0 && (
                        <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                                <div className={`w-2 h-2 rounded-full ${getPasswordStrength() === 'strong' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span className={`text-sm font-medium ${getPasswordStrength() === 'strong' ? 'text-green-600' : 'text-red-500'}`}>
                                    Password strength: {getPasswordStrength()}
                                </span>
                            </div>
                            <div className="flex items-center gap-2 mb-1">
                                <div className={`w-2 h-2 rounded-full ${hasMinLength() ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span className={`text-sm ${hasMinLength() ? 'text-green-600' : 'text-red-500'}`}>
                                    At least 8 characters
                                </span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${hasNumberOrSymbol() ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span className={`text-sm ${hasNumberOrSymbol() ? 'text-green-600' : 'text-red-500'}`}>
                                    Contains a number or symbol
                                </span>
                            </div>
                        </div>
                    )}

                    {/* Confirm Password Input */}
                    <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg ${confirmPasswordFocused
                        ? formErrors.confirmPassword || formErrors.passwordMismatch
                            ? 'border-red-500 border-2'
                            : 'border-brand-black-200 border-2'
                        : formErrors.confirmPassword || formErrors.passwordMismatch
                            ? 'border-red-500 border'
                            : 'border border-brand-gray-960'
                        }`}>
                        <label
                            className={`
                                absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                                ${confirmPasswordFocused || confirmPassword.length > 0
                                    ? formErrors.confirmPassword || formErrors.passwordMismatch
                                        ? 'top-0 text-xs text-red-500'
                                        : 'top-0 text-xs text-brand-gray-970'
                                    : formErrors.confirmPassword || formErrors.passwordMismatch
                                        ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                        : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                                }
                            `}
                        >
                            Confirm password
                        </label>
                        <input
                            type={showConfirmPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => {
                                setConfirmPassword(e.target.value)
                                clearFieldError('confirmPassword')
                            }}
                            onFocus={() => setConfirmPasswordFocused(true)}
                            onBlur={() => setConfirmPasswordFocused(false)}
                            className="border-none w-full h-full outline-none text-xl pr-16"
                        />
                        <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 text-brand-black-300 underline text-sm font-normal"
                        >
                            {showConfirmPassword ? 'Hide' : 'Show'}
                        </button>
                    </div>

                    {formErrors.passwordMismatch && (
                        <p className="text-red-500 text-sm mt-2">Passwords must match</p>
                    )}
                    {formErrors.password && (
                        <p className="text-red-500 text-sm mt-1">Password must meet requirements</p>
                    )}
                    {formErrors.confirmPassword && !formErrors.passwordMismatch && (
                        <p className="text-red-500 text-sm mt-1">Confirm password is required</p>
                    )}
                </div>
            )}

            {/* Terms and Continue Button */}
            <div>
                <p className="text-gray-600 text-sm mb-4">
                    By selecting <span className="font-medium">Agree and continue</span>, I agree to <Link href="#" className="text-brand-blue-300 underline font-bold">YouHook&apos;s Terms of Service</Link> and have acknowledge the <Link href="#" className="text-brand-blue-300 underline font-bold">Privacy Policy</Link>.
                </p>
                <button
                    onClick={handleFinishSignup}
                    className="bg-background-custom3 cursor-pointer text-white text-lg font-medium rounded-xl px-6 py-4 w-full">
                    Agree and continue
                </button>
            </div>
        </div>
    )
} 