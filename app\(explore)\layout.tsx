'use client'
import { Suspense } from "react";
import FooterSection from "@/components/Sections/FooterSection";
import HeaderInner from "@/components/Sections/HeaderInner";

export default function ExploreLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <>
            <Suspense fallback={<div className="h-[120px]" />}>
                <HeaderInner isInnerpages={true} />
            </Suspense>
            {children}
            <FooterSection />
        </>
    );
}
