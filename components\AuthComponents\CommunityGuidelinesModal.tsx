import { ChevronRight } from "lucide-react"

interface CommunityGuidelinesModalProps {
    onAgree: () => void
    onDecline: () => void
}

export default function CommunityGuidelinesModal({ onAgree, onDecline }: CommunityGuidelinesModalProps) {
    return (
        <div className="px-8 pb-8">
            <div className="mb-8">
                <h2 className="text-brand-blue-400 text-3xl font-extrabold mb-6">YouHook</h2>

                <div className="text-left">
                    <h3 className="text-brand-black-300 text-lg font-medium mb-4">Community Guidelines</h3>
                    <p className="text-brand-black-300 text-3xl font-medium mb-6">
                        YouHook is a community of like minded hard working individuals
                    </p>

                    <p className="text-brand-black-300 text-base mb-6">
                        To ensure this we ask all users to commit to our principals:
                    </p>

                    <div className="space-y-4">
                        <div>
                            <p className="text-brand-black-300 text-base font-bold">
                                Respect Everyone: <span className="font-normal">Treat all members with fairness and kindness, free from discrimination.</span>
                            </p>
                        </div>

                        <div>
                            <p className="text-brand-black-300 text-base font-bold">
                                Be Honest: <span className="font-normal">Provide accurate information and be transparent in your transactions.</span>
                            </p>
                        </div>

                        <div>
                            <p className="text-brand-black-300 text-base font-bold">
                                Work Hard: <span className="font-normal">Commitment and effort lead to success- dedicate yourself to doing your best.</span>
                            </p>
                        </div>

                        <div>
                            <p className="text-brand-black-300 text-base font-bold">
                                Communicate: <span className="font-normal">Stay active, engage with other users, and respond promptly.</span>
                            </p>
                        </div>
                    </div>

                    <div className="pt-2">
                        <button
                            className="text-brand-black-300 text-base font-medium underline"
                        >
                            Learn more <ChevronRight className="w-4 h-4 inline-block" />
                        </button>
                    </div>
                </div>
            </div>

            <div className="space-y-3">
                <button
                    onClick={onAgree}
                    className="w-full h-[60px] bg-background-custom3 text-white rounded-lg text-lg font-medium cursor-pointer"
                >
                    Agree and continue
                </button>

                <button
                    onClick={onDecline}
                    className="w-full h-[60px] border border-brand-black-300 text-brand-black-300 rounded-lg text-lg font-medium cursor-pointer"
                >
                    Decline
                </button>
            </div>
        </div>
    )
} 