import { X } from "lucide-react"

interface CompletionScreenProps {
    onClose: () => void
}

export default function CompletionScreen({ onClose }: CompletionScreenProps) {
    return (
        <div>
            <div className="">
                {/* Header with close button */}
                <div className="flex items-center justify-between border-b border-brand-gray-970 pb-6 px-8">
                    <button onClick={onClose} className="cursor-pointer text-black/50">
                        <X size={20} />
                    </button>
                    <h2 className="text-brand-black-300 text-xl font-medium">
                        All done!
                    </h2>
                    <div className="w-5" /> {/* Spacer for alignment */}
                </div>

                {/* Content */}
                <div className="mb-20 px-8 pt-10">
                    <h3 className="text-brand-black-300 text-2xl font-medium mb-6">
                        We&apos;re reviewing your ID
                    </h3>

                    <p className="text-brand-black-300 text-base leading-relaxed">
                        Thank you for completing this important step—your ID is now under review, and we&apos;ll notify you once it&apos;s verified.
                    </p>
                </div>

                {/* Done Button */}
                <div className="flex justify-center px-8 pb-8">
                    <button
                        onClick={onClose}
                        className="w-full h-[60px] bg-brand-black-200 text-white rounded-lg text-lg font-medium cursor-pointer hover:bg-brand-black-300 transition-colors"
                    >
                        Done
                    </button>
                </div>
            </div>
        </div>
    )
} 