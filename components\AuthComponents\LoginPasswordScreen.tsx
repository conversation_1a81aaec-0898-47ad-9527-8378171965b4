import { useState } from "react"
import { AlertPolygonRed } from "../Icons"

interface LoginPasswordScreenProps {
    onLogin: (password: string) => void
    onForgotPassword: () => void
    hasError?: boolean
}

export default function LoginPasswordScreen({
    onLogin,
    onForgotPassword,
    hasError = false
}: LoginPasswordScreenProps) {
    const [password, setPassword] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const [passwordFocused, setPasswordFocused] = useState(false)

    const handleSubmit = () => {
        if (password.trim()) {
            onLogin(password)
        }
    }

    return (
        <div className="px-8 pb-8 pt-4">
            {hasError && (
                <div className="flex items-center gap-8 bg-brand-gray-750 border border-brand-gray-1001 rounded-lg p-4 mb-6">
                    <AlertPolygonRed />
                    <span className="text-brand-gray-950 text-lg">
                        Invalid login credentials, please try again
                    </span>
                </div>
            )}

            <div className="mb-6">
                <h3 className="text-black text-lg font-medium mb-4">
                    Password
                </h3>

                <div className={`relative w-full h-[60px] flex items-center pl-4 pr-2 transition-all duration-300 rounded-lg ${passwordFocused
                    ? hasError
                        ? 'border-red-500 border-2'
                        : 'border-brand-black-200 border-2'
                    : hasError
                        ? 'border-red-500 border'
                        : 'border border-brand-gray-960'
                    }`}>
                    <label
                        className={`
                            absolute left-3 bg-white block text-brand-gray-970 transition-all pointer-events-none 
                            ${passwordFocused || password.length > 0
                                ? hasError
                                    ? 'top-0 text-xs text-red-500'
                                    : 'top-0 text-xs text-brand-gray-970'
                                : hasError
                                    ? 'top-1/2 -translate-y-1/2 text-xl text-red-500'
                                    : 'top-1/2 -translate-y-1/2 text-xl text-brand-gray-970'
                            }
                        `}
                    >
                        Password
                    </label>
                    <input
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        onFocus={() => setPasswordFocused(true)}
                        onBlur={() => setPasswordFocused(false)}
                        className="border-none w-full h-full outline-none text-xl pr-16"
                    />
                    <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 text-brand-black-300 underline text-sm font-normal"
                    >
                        {showPassword ? 'Hide' : 'Show'}
                    </button>
                </div>
            </div>

            <div className="space-y-4">
                <button
                    onClick={handleSubmit}
                    disabled={!password.trim()}
                    className={`w-full h-[60px] text-white rounded-lg text-lg font-medium transition-colors ${password.trim()
                        ? 'bg-background-custom3 cursor-pointer'
                        : 'bg-background-disabled cursor-not-allowed'
                        }`}
                >
                    Log in
                </button>

                <div className="text-left">
                    <button
                        onClick={onForgotPassword}
                        className="text-black font-medium underline cursor-pointer"
                    >
                        Forgot password?
                    </button>
                </div>
            </div>
        </div>
    )
} 