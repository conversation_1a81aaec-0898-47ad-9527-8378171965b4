import Link from "next/link";
import { ShieldCheckIcon } from "../Icons";
import Image from "next/image";


export default function CoverageSection() {
    return (
        <section className="w-full h-[360px] flex items-center relative bg-white mt-40">
            <div className="absolute bottom-0 left-0 w-full h-full">
                <Image src={"/images/coverage-bg.png"} alt="Coverage" width={825.78} height={1105.83} />
            </div>
            <div className="container w-[1200px] mx-auto px-4 ">
                <div className="w-[400px] ml-auto">
                    <h2 className="text-brand-600 text-4xl font-bold flex items-center gap-2 mb-4">
                        <span className="text-brand-1000"><ShieldCheckIcon /></span>
                        <span className="font-bold text-brand-1000">YouHook</span>
                        <span className="font-light text-brand-gray-50"> coverage</span>
                    </h2>
                    <p className="text-brand-black-50 text-xl font-regular">
                        Every rental on YouHook is insured with guaranteed coverage. Learn more about claims in your account. <Link href={""} className="text-brand-blue-50 font-medium underline">Insurance claims</Link>
                    </p>
                </div>
            </div>
        </section >
    );
} 