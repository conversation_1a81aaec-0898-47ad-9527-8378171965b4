import Image from "next/image"
import { EmailIcon } from "../Icons"

interface AlreadySignupEmailScreenProps {
    email: string
    onLogin: () => void
    onUseAnother: () => void
}

export default function AlreadySignupEmailScreen({
    email,
    onLogin,
    onUseAnother
}: AlreadySignupEmailScreenProps) {
    return (
        <div className="px-8 pb-8 pt-4">
            <div className="text-center">
                <div className="mb-8">
                    <div className="flex justify-center mb-6">
                        <Image
                            src="/images/Idea4.png"
                            alt="Profile"
                            width={180}
                            height={180}
                            className="rounded-full w-[180px] h-[180px] object-cover"
                        />
                        {/* <div className="w-[120px] h-[120px] rounded-full bg-black flex items-center justify-center">
                            <div className="w-[80px] h-[80px] rounded-full bg-white flex items-center justify-center">
                            </div>
                        </div> */}
                    </div>

                    <div className="flex items-center justify-center gap-2 text-gray-600 mb-20">
                        <span><EmailIcon /></span>
                        <span className="text-lg">
                            {email.length > 4
                                ? email.slice(0, 2) + '•'.repeat(email.length - 4) + email.slice(-10)
                                : email.replace(/./g, '•')
                            }
                        </span>
                    </div>

                    <button
                        onClick={onLogin}
                        className="w-full h-[60px] border text-black border-brand-gray-990 flex items-center justify-center gap-2 rounded-lg text-lg relative cursor-pointer mb-4"
                    >
                        <span className="absolute left-4">
                            <EmailIcon />
                        </span>
                        <span>
                            Log in
                        </span>

                    </button>

                    <div className="text-left text-black">
                        <span>Not you? </span>
                        <button
                            onClick={onUseAnother}
                            className="font-medium underline cursor-pointer"
                        >
                            Use another account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
} 