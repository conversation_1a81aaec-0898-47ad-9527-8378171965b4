import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Title,
} from "@/components/ui/dialog"
import { <PERSON>, ChevronLeft } from "lucide-react"
import { useState } from "react"
import Image from "next/image"
import CreateProfileScreen from "./CreateProfileScreen"
import ProfileUploadScreen from "./ProfileUploadScreen"
import IDVerificationScreen from "./IDVerificationScreen"
import IDTypeSelectionScreen from "./IDTypeSelectionScreen"
import IDUploadScreen from "./IDUploadScreen"
import PassportUploadScreen from "./PassportUploadScreen"
import IDCameraScreen from "./IDCameraScreen"
import SelfieVerificationScreen from "./SelfieVerificationScreen"
import SelfieCameraScreen from "./SelfieCameraScreen"
import CompletionScreen from "./CompletionScreen"


interface ProfileCreationModalProps {
    isOpen: boolean
    onClose: () => void
    onComplete: (profileData: { profileImage: string | null }) => void
}

type ProfileStep = "welcome" | "upload" | "bio" | "bio-generated" | "id-verification" | "id-type-selection" | "id-upload" | "passport-upload" | "id-camera" | "id-camera-back" | "selfie-verification" | "selfie-camera" | "completion" | "completed"

export default function ProfileCreationModal({ isOpen, onClose, onComplete }: ProfileCreationModalProps) {
    const [currentStep, setCurrentStep] = useState<ProfileStep>("welcome")
    const [uploadedImage, setUploadedImage] = useState<string | null>(null)
    const [bioText, setBioText] = useState("")
    const [selectedIDType, setSelectedIDType] = useState<"drivers-license" | "passport" | "identity-card" | null>(null)
    const [verificationType, setVerificationType] = useState<"upload" | "camera" | null>(null)
    const [capturedIDPhoto, setCapturedIDPhoto] = useState<string | null>(null)
    const [capturedIDPhotoBack, setCapturedIDPhotoBack] = useState<string | null>(null)
    const [capturedSelfie, setCapturedSelfie] = useState<string | null>(null)

    const handleContinueFromWelcome = () => {
        setCurrentStep("upload")
    }

    // Unused but kept for potential future use
    // const handleImageUpload = (imageData: string) => {
    //     // Complete profile creation with the uploaded image
    //     onComplete({ profileImage: imageData })
    //     onClose()
    // }

    const handleSkipUpload = () => {
        // Move to bio screen instead of completing
        setCurrentStep("bio")
    }

    const handleContinueFromUpload = (imageData: string) => {
        // Store image and move to bio screen
        setUploadedImage(imageData)
        setCurrentStep("bio")
    }

    const handleGenerateBio = () => {
        // Generate static bio and move to bio-generated screen
        const staticBio = "Passionate about connecting with like-minded individuals and exploring new opportunities. I believe in building meaningful relationships and making a positive impact in my community."
        setBioText(staticBio)
        setCurrentStep("bio-generated")
    }

    const handleBioComplete = () => {
        // Move to ID verification instead of completing
        setCurrentStep("id-verification")
    }

    const handleBioDone = () => {
        // Go back to bio screen after editing generated bio
        setCurrentStep("bio")
    }

    const handleIDVerificationContinue = (verificationMethod: "upload" | "camera") => {
        // Store verification method and go to ID type selection screen
        setVerificationType(verificationMethod)
        setCurrentStep("id-type-selection")
    }

    const handleIDVerificationSkip = () => {
        // Complete profile creation without ID verification
        onComplete({ profileImage: uploadedImage })
        onClose()
    }

    const handleIDTypeSelectionContinue = (idType: "drivers-license" | "passport" | "identity-card") => {
        // Store selected ID type and move to appropriate screen based on verification method
        setSelectedIDType(idType)

        if (verificationType === "camera") {
            // Camera flow - go to camera screen for all ID types
            setCurrentStep("id-camera")
        } else {
            // Upload flow - different screens for passport vs other IDs
            if (idType === "passport") {
                setCurrentStep("passport-upload")
            } else {
                setCurrentStep("id-upload")
            }
        }
    }

    const handleIDUploadContinue = () => {
        // Move to selfie verification after ID upload
        setCurrentStep("selfie-verification")
    }

    const handlePassportUploadContinue = () => {
        // Move to selfie verification after passport upload
        setCurrentStep("selfie-verification")
    }

    const handleIDCameraContinue = (capturedImage: string) => {
        // Store captured image and stay on camera screen for review
        setCapturedIDPhoto(capturedImage)
    }

    const handleIDCameraComplete = () => {
        // Move to back camera for non-passport IDs, selfie verification for passport
        if (selectedIDType !== "passport") {
            setCurrentStep("id-camera-back")
        } else {
            // For passport, go directly to selfie verification (no back photo needed)
            setCurrentStep("selfie-verification")
        }
    }

    const handleIDCameraBackContinue = (capturedImage: string) => {
        // Store back captured image
        setCapturedIDPhotoBack(capturedImage)
    }

    const handleIDCameraBackComplete = () => {
        // Move to selfie verification after back photo
        setCurrentStep("selfie-verification")
    }

    const handleSelfieVerificationContinue = () => {
        // Move to selfie camera screen
        setCurrentStep("selfie-camera")
    }

    const handleSelfieCameraContinue = (capturedImage: string) => {
        // Store captured selfie and stay on camera screen for review
        setCapturedSelfie(capturedImage)
    }

    const handleSelfieCameraComplete = () => {
        // Move to completion screen
        setCurrentStep("completion")
    }

    const handleCompletionClose = () => {
        // Complete profile creation with selfie
        if (capturedSelfie) {
            onComplete({ profileImage: capturedIDPhoto })
            onClose()
        }
    }

    const handleSelfiePhotoRetake = () => {
        // Clear captured selfie for retaking
        setCapturedSelfie(null)
    }

    const handleIDPhotoBackRetake = () => {
        // Clear captured back photo for retaking
        setCapturedIDPhotoBack(null)
    }

    const handleIDPhotoRetake = () => {
        // Clear captured photo for retaking and restart camera
        setCapturedIDPhoto(null)
    }

    // Unused but kept for potential future use
    // const handleIDPhotoConfirm = () => {
    //     // Complete profile creation after confirming photo
    //     onComplete({ profileImage: uploadedImage })
    //     onClose()
    // }

    const handleBack = () => {
        if (currentStep === "upload") {
            setCurrentStep("welcome")
        } else if (currentStep === "bio") {
            setCurrentStep("upload")
        } else if (currentStep === "bio-generated") {
            setCurrentStep("bio")
        } else if (currentStep === "id-verification") {
            setCurrentStep("bio")
        } else if (currentStep === "id-type-selection") {
            setCurrentStep("id-verification")
        } else if (currentStep === "id-upload") {
            setCurrentStep("id-type-selection")
        } else if (currentStep === "passport-upload") {
            setCurrentStep("id-type-selection")
        } else if (currentStep === "id-camera") {
            setCurrentStep("id-type-selection")
        } else if (currentStep === "id-camera-back") {
            setCurrentStep("id-camera")
        } else if (currentStep === "selfie-verification") {
            // Go back to the appropriate screen based on ID type and verification method
            if (verificationType === "camera") {
                if (selectedIDType === "passport") {
                    setCurrentStep("id-camera")
                } else {
                    setCurrentStep("id-camera-back")
                }
            } else {
                // Upload path
                if (selectedIDType === "passport") {
                    setCurrentStep("passport-upload")
                } else {
                    setCurrentStep("id-upload")
                }
            }
        } else if (currentStep === "selfie-camera") {
            setCurrentStep("selfie-verification")
        } else if (currentStep === "completion") {
            setCurrentStep("selfie-camera")
        }
    }

    const getStepTitle = () => {
        switch (currentStep) {
            case "welcome":
                return "Create your profile"
            case "upload":
                return "Step 1/3"
            case "bio":
            case "bio-generated":
                return "Step 2/3"
            case "id-verification":
                return "Last step"
            case "id-type-selection":
                return "Verification"
            case "id-upload":
            case "passport-upload":
            case "id-camera":
            case "id-camera-back":
            case "selfie-verification":
            case "selfie-camera":
                return "Verification"
            case "completion":
                return ""
            default:
                return ""
        }
    }

    const showHeader = currentStep !== "welcome" && currentStep !== "completion"
    const showReloadIcon = (currentStep === "id-camera" && capturedIDPhoto) || (currentStep === "id-camera-back" && capturedIDPhotoBack) || (currentStep === "selfie-camera" && capturedSelfie)

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent
                onEscapeKeyDown={(e) => e.preventDefault()}
                className="signup-modal-content px-0 min-w-[650px] shadow-custom-2 pb-0 overflow-y-auto max-h-[70vh] no-scroll"
            >
                {showHeader && (
                    <DialogHeader className="flex flex-row items-center justify-between border-b border-brand-gray-970 pb-4 px-8">
                        {showReloadIcon ? (
                            <button
                                onClick={
                                    currentStep === "id-camera" ? handleIDPhotoRetake :
                                        currentStep === "id-camera-back" ? handleIDPhotoBackRetake :
                                            handleSelfiePhotoRetake
                                }
                                className="cursor-pointer"
                            >
                                <Image src="/images/reload.png" alt="Retake" width={20} height={20} />
                            </button>
                        ) : (
                            <button onClick={handleBack} className="cursor-pointer text-black/50">
                                <ChevronLeft size={20} />
                            </button>
                        )}
                        <DialogTitle className="text-black text-xl font-medium text-center">
                            {getStepTitle()}
                        </DialogTitle>
                        {false ? (
                            <button onClick={onClose} className="cursor-pointer text-black/50">
                                <X size={20} />
                            </button>
                        ) : <div />}


                    </DialogHeader>
                )}

                <div>
                    {currentStep === "welcome" && (
                        <CreateProfileScreen
                            onContinue={handleContinueFromWelcome}
                            onClose={onClose}
                        />
                    )}

                    {currentStep === "upload" && (
                        <ProfileUploadScreen
                            onImageUpload={handleContinueFromUpload}
                            onSkip={handleSkipUpload}
                            onBack={handleBack}
                            currentScreen="upload"
                            uploadedImage={uploadedImage}
                            bioText={bioText}
                            onGenerateBio={handleGenerateBio}
                            onBioComplete={handleBioComplete}
                            onBioTextChange={setBioText}
                        />
                    )}

                    {currentStep === "bio" && (
                        <ProfileUploadScreen
                            onImageUpload={handleContinueFromUpload}
                            onSkip={handleSkipUpload}
                            onBack={handleBack}
                            currentScreen="bio"
                            uploadedImage={uploadedImage}
                            bioText={bioText}
                            onGenerateBio={handleGenerateBio}
                            onBioComplete={handleBioComplete}
                            onBioTextChange={setBioText}
                            onBioDone={handleBioDone}
                        />
                    )}

                    {currentStep === "bio-generated" && (
                        <ProfileUploadScreen
                            onImageUpload={handleContinueFromUpload}
                            onSkip={handleSkipUpload}
                            onBack={handleBack}
                            currentScreen="bio-generated"
                            uploadedImage={uploadedImage}
                            bioText={bioText}
                            onGenerateBio={handleGenerateBio}
                            onBioComplete={handleBioComplete}
                            onBioTextChange={setBioText}
                            onBioDone={handleBioDone}
                        />
                    )}

                    {currentStep === "id-verification" && (
                        <IDVerificationScreen
                            onContinue={handleIDVerificationContinue}
                            onSkip={handleIDVerificationSkip}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "id-type-selection" && (
                        <IDTypeSelectionScreen
                            onContinue={handleIDTypeSelectionContinue}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "id-upload" && selectedIDType && (
                        <IDUploadScreen
                            idType={selectedIDType}
                            onContinue={handleIDUploadContinue}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "passport-upload" && (
                        <PassportUploadScreen
                            onContinue={handlePassportUploadContinue}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "id-camera" && selectedIDType && (
                        <IDCameraScreen
                            idType={selectedIDType}
                            onContinue={handleIDCameraContinue}
                            onComplete={handleIDCameraComplete}
                            onBack={handleBack}
                            onRetake={handleIDPhotoRetake}
                            capturedImage={capturedIDPhoto}
                        />
                    )}

                    {currentStep === "id-camera-back" && selectedIDType && (
                        <IDCameraScreen
                            idType={selectedIDType}
                            onContinue={handleIDCameraBackContinue}
                            onComplete={handleIDCameraBackComplete}
                            onBack={handleBack}
                            onRetake={handleIDPhotoBackRetake}
                            capturedImage={capturedIDPhotoBack}
                            isBackSide={true}
                        />
                    )}

                    {currentStep === "selfie-verification" && (
                        <SelfieVerificationScreen
                            onContinue={handleSelfieVerificationContinue}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "selfie-camera" && (
                        <SelfieCameraScreen
                            onContinue={handleSelfieCameraContinue}
                            onComplete={handleSelfieCameraComplete}
                            onBack={handleBack}
                            onRetake={handleSelfiePhotoRetake}
                            capturedImage={capturedSelfie}
                        />
                    )}

                    {currentStep === "completion" && (
                        <CompletionScreen
                            onClose={handleCompletionClose}
                        />
                    )}


                </div>
            </DialogContent>
        </Dialog>
    )
} 